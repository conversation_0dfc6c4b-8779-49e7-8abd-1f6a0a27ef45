
/** chooseus-section **/

.chooseus-section{
  position: relative;
  padding: 114px 0px 160px 0px;
  background-color: #0E1136;
}

.chooseus-section .bg-layer{
  position: absolute;
  top: 0px;
  right: 0px;
  width: calc(50% - 224px);
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.chooseus-block-one .inner-box{
  position: relative;
  display: block;
  padding-left: 105px;
  padding-bottom: 30px;
  margin-bottom: 30px;
}

.chooseus-block-one .inner-box .icon-box{
  position: absolute;
  left: 0px;
  top: 0px;
  font-size: 80px;
  color: #fff;
  transition: all 500ms ease;
}

.chooseus-block-one .inner-box:hover .icon-box{
  color: var(--secondary-color);
}

.chooseus-block-one .inner-box h3{
  display: block;
  font-size: 26px;
  line-height: 36px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10px;
}

.chooseus-block-one .inner-box p{
  color: #fff;
}

.chooseus-block:first-child .chooseus-block-one .inner-box,
.chooseus-block:nth-child(2) .chooseus-block-one .inner-box{
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chooseus-section .pattern-layer{
  position: absolute;
  left: 0px;
  bottom: 0px;
  width: 490px;
  height: 570px;
  background-repeat: no-repeat;
  opacity: 0.7;
}


/** chooseus-style-two **/

.chooseus-style-two{
  position: relative;
  padding: 114px 0px 50px 0px;
}

.chooseus-style-two .bg-layer{
  position: absolute;
  top: 0px;
  left: 0px;
  width: calc(50% - 224px);
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.chooseus-style-two .chooseus-block-one .inner-box .icon-box{
  color: var(--theme-color);
}

.chooseus-style-two .chooseus-block-one .inner-box:hover .icon-box{
  color: var(--secondary-color) !important;
}

.chooseus-style-two .chooseus-block-one .inner-box h3{
  color: #0E1136;
}

.chooseus-style-two .chooseus-block-one .inner-box p{
  color: #676767;
}

.chooseus-style-two .chooseus-block:first-child .chooseus-block-one .inner-box, 
.chooseus-style-two .chooseus-block:nth-child(2) .chooseus-block-one .inner-box{
  border-color: #fff;
}







/** RTL-CSS **/

.rtl .chooseus-section .bg-layer{
  right: inherit;
  left: 0px;
}

.rtl .chooseus-block-one .inner-box{
  padding-left: 0px;
  padding-right: 105px;
}

.rtl .chooseus-block-one .inner-box .icon-box{
  left: inherit;
  right: 0px;
}



/** RESPONSIVE-CSS **/


@media only screen and (max-width: 1200px){



}

@media only screen and (max-width: 991px){

  .chooseus-section .bg-layer,
  .chooseus-style-two .bg-layer{
    display: none;
  }

  .chooseus-style-two .content-box{
    margin-left: 0px;
  }

}

@media only screen and (max-width: 767px){

  .chooseus-section{
    padding: 64px 0px 110px 0px;
  }

  .chooseus-style-two{
    padding: 64px 0px 0px 0px;
  }

}

@media only screen and (max-width: 599px){

}

@media only screen and (max-width: 499px){

  .chooseus-block-one .inner-box{
    padding-left: 0px;
  }

  .rtl .chooseus-block-one .inner-box{
    padding-right: 0px;
  }

  .chooseus-block-one .inner-box .icon-box{
    position: relative;
    margin-bottom: 15px;
  }

}












































