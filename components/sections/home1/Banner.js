// import React from "react";
// import Link from "next/link";
// export default function Banner() {
//   return (
//     <section className="banner-section p_relative">
//       <div
//         className="pattern-layer wow slideInDown animated"
//         data-wow-delay="00ms"
//         data-wow-duration="1500ms"
//         style={{ backgroundImage: "url(assets/images/shape/shape-1.png)" }}
//       ></div>
//       <div className="auto-container">
//         <div className="row align-items-center">
//           <div className="col-lg-6 col-md-12 col-sm-12 content-column">
//             <div className="content-box">
//               <span className="upper-text">
//                 Highest level of service you can find
//               </span>
//               <h2>
//                 Take <span>Care of Your</span> Health Now.
//               </h2>
//               <p>
//                 All the medical care you need is at <span style={{fontWeight: 'bold'}}>
//                   Al Zain Medical Center.
//                 </span>
//               </p>
//               <div className="btn-box">
//                 <Link
//                   href="#appointment"

//                   className="theme-btn btn-two"
//                 >
//                   <span>Explore Our Service</span>
//                 </Link>
//               </div>
//             </div>
//           </div>
//           <div className="col-lg-6 col-md-12 col-sm-12 image-column">
//             <div className="image-box">
//               <div className="shape">
//                 <div
//                   className="shape-1"
//                   style={{
//                     backgroundImage: "url(assets/images/shape/shape-2.png)",
//                     zIndex: 50,
//                   }}
//                 ></div>
//                 <div
//                   className="shape-2 float-bob-x"
//                   style={{
//                     backgroundImage: "url(assets/images/shape/shape-3.png)",
//                     zIndex: 50,
//                   }}
//                 ></div>
//                 <div
//                   className="shape-3"
//                   style={{
//                     backgroundImage: "url(assets/images/shape/shape-4.png)",
//                     zIndex: 50,
//                   }}
//                 ></div>
//                 <div
//                   className="shape-4"
//                   style={{
//                     backgroundImage: "url(assets/images/shape/shape-5.png)",
//                   }}
//                 ></div>
//               </div>
//               <figure className="image float-bob-y" style={{ zIndex: 10 }}>
//                 <img src="assets/images/banner/banner-img-1.jpg" width={'150px'} style={{ borderRadius: '25px' }} alt="" />
//               </figure>
//             </div>
//           </div>
//         </div>
//       </div>
//     </section>
//   );
// }

"use client"
import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useTranslation } from 'react-i18next';

export default function Banner() {
  const { t } = useTranslation('common');
  const [currentSlide, setCurrentSlide] = useState(0);
  const images = [
    "/assets/images/banner/01.png",
    "/assets/images/banner/02.png", // Same URL for demo
    "/assets/images/banner/03.png", // Same URL for demo
    "/assets/images/banner/04.png"  // Same URL for demo
  ];

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % images.length);
    }, 3000); // Change slide every 3 seconds
    return () => clearInterval(interval);
  }, [images.length]);

  return (
    <section className="banner-section p_relative">
      <div
        className="pattern-layer wow slideInDown animated"
        data-wow-delay="00ms"
        data-wow-duration="1500ms"
        style={{ backgroundImage: "url(assets/images/shape/shape-1.png)" }}
      ></div>
      <div className="auto-container">
        <div className="row align-items-center">
          <div className="col-lg-6 col-md-12 col-sm-12 content-column">
            <div className="content-box">
              <span className="upper-text">
                {t('banner.upperText')}
              </span>
              <h2>
                {t('banner.takeText')} <span>{t('banner.careOfYour')}</span> {t('banner.healthNow')}
              </h2>
              <p>
                {t('banner.description')}
              </p>
              <div className="btn-container">
                <div className="btn-box">
                  <Link href="#appointment" className="theme-btn btn-two">
                    <span>{t('banner.bookAppointment')}</span>
                  </Link>
                </div>
                {/* <div className="btn-box">
                  <Link href="#services" className="theme-btn btn-two" >
                    <span>Explore Our Service</span>
                  </Link>
                </div> */}
              </div>
            </div>
          </div>
          <div className="col-lg-6 col-md-12 col-sm-12 image-column">
            <div className="image-box">
              {/* <div className="shape"> */}
                {/* <div
                  className="shape-1"
                  style={{
                    backgroundImage: "url(assets/images/shape/shape-2.png)",
                    zIndex: 50,
                  }}
                ></div>
                <div
                  className="shape-2 float-bob-x"
                  style={{
                    backgroundImage: "url(assets/images/shape/shape-3.png)",
                    zIndex: 50,
                  }}
                ></div>
                <div
                  className="shape-3"
                  style={{
                    backgroundImage: "url(assets/images/shape/shape-4.png)",
                    zIndex: 50,
                  }}
                ></div>
                <div
                  className="shape-4"
                  style={{
                    backgroundImage: "url(assets/images/shape/shape-5.png)",
                  }}
                ></div>
              </div> */}

              {/* Image Slider */}
              <div className="slider-container" style={{
                position: 'relative',
                width: 'fit',
                height: 'fit',
                overflow: 'hidden',
                borderRadius: '25px'
              }}>
                <div className="slider-track" style={{
                  display: 'flex',
                  gap: '10px',
                  width: `${images.length * 100}%`,
                  height: '100%',
                  transform: `translateX(-${currentSlide * (102 / images.length)}%)`,
                  transition: 'transform 0.5s ease-in-out',
                }}>
                  {images.map((image, index) => (
                    <div
                      key={index}
                      className="slide"
                      style={{
                        width: `${100 / images.length}%`,
                        flexShrink: 0,
                        height: '100%',
                        borderRadius: '30px',
                      }}
                    >
                      <img
                        src={image}
                        alt={`Slide ${index + 1}`}
                        style={{
                          width: '100%',
                          height: '100%',
                          borderRadius: '30px',
                          objectFit: 'contain',
                          borderRadius: '25px'
                        }}
                      />
                    </div>
                  ))}
                </div>

                {/* Navigation dots */}
                <div style={{
                  position: 'absolute',
                  bottom: '20px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  display: 'flex',
                  gap: '8px',
                  zIndex: 20
                }}>
                  {images.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      style={{
                        width: '12px',
                        height: '12px',
                        borderRadius: '50%',
                        border: 'none',
                        cursor: 'pointer',
                        backgroundColor: currentSlide === index ? '#fff' : 'rgba(255,255,255,0.5)',
                        transition: 'background-color 0.3s'
                      }}
                      aria-label={`Go to slide ${index + 1}`}
                    />
                  ))}
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </section>
  );
}