'use client';
import React from 'react';
import Link from "next/link"
import { useTranslation } from 'react-i18next';
import { getAllDoctors } from '@/data/doctors';

export default function Team() {
  const { t } = useTranslation('common');
  const teamMembers = getAllDoctors();

  return (
    <section className="team-section sec-pad cen tred" id='team'>
      {/* <div className="pattern-layer">
        <div className="pattern-1" style={{ backgroundImage: 'url(assets/images/shape/shape-13.png)' }}></div>
        <div className="pattern-2" style={{ backgroundImage: 'url(assets/images/shape/shape-14.png)' }}></div>
      </div>
      <div className="shape">
        <div className="shape-1 float-bob-y" style={{ backgroundImage: 'url(assets/images/shape/shape-15.png)' }}></div>
        <div className="shape-2"></div>
        <div className="shape-3 float-bob-x" style={{ backgroundImage: 'url(assets/images/shape/shape-16.png)' }}></div>
      </div> */}
      <div className="auto-container" >
        <div className="sec-title mb_50">
          <span className="sub-title">{t('team.subtitle')}</span>
          <h2>{t('team.mainTitle')}</h2>
        </div>
        <div className="row clearfix" >
          {teamMembers.map((member, index) => (
            <div key={index} className="col-lg-3 col-md-6 col-sm-12 team-block">
              <div className="team-block-one wow fadeInUp animated" data-wow-delay={`${index * 200}ms`} data-wow-duration="1500ms">
                <Link href={`/doctor/${member.id}`} className="doctor-card-link">
                  <div className="inner-box">
                    <div className="image-box">
                      <figure className="image"><img src={member.image} style={{ objectFit: 'cover', width: '100%', height: '280px' }} alt={`${member.name}'s photo`} /></figure>
                      {/* <ul className="social-links clearfix">
                        <li><Link href="/#"><i className="icon-4"></i></Link></li>
                        <li><Link href="/#"><i className="icon-5"></i></Link></li>
                        <li><Link href="/#"><i className="icon-6"></i></Link></li>
                        <li><Link href="/#"><i className="icon-7"></i></Link></li>
                      </ul> */}
                    </div>
                    <div className="lower-content">
                      <h3>{member.name} <span className="view-profile-icon">→</span></h3>
                      <span className="designation">{t(`team.roles.${member.roleKey}`)}</span>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Custom Styles for Clickable Cards */}
      <style jsx>{`
        .doctor-card-link {
          display: block;
          text-decoration: none;
          color: inherit;
          transition: all 0.3s ease;
        }

        .doctor-card-link:hover {
          text-decoration: none;
          color: inherit;
        }

        .doctor-card-link .inner-box {
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
        }

        .doctor-card-link .inner-box::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(75, 183, 241, 0.05);
          opacity: 0;
          transition: all 0.3s ease;
          border-radius: 20px;
          pointer-events: none;
        }

        .doctor-card-link:hover .inner-box {
          transform: translateY(-5px);
          box-shadow: 0px 10px 80px rgba(0, 0, 0, 0.15);
        }

        .doctor-card-link:hover .inner-box::after {
          opacity: 1;
        }

        .doctor-card-link .lower-content h3 {
          color: #0e1136;
          transition: all 0.3s ease;
        }

        .doctor-card-link:hover .lower-content h3 {
          color: var(--theme-color);
        }

        .view-profile-icon {
          opacity: 0;
          transform: translateX(-10px);
          transition: all 0.3s ease;
          font-size: 18px;
          color: var(--theme-color);
          margin-left: 8px;
        }

        .doctor-card-link:hover .view-profile-icon {
          opacity: 1;
          transform: translateX(0);
        }

        /* RTL Support for icon */
        .rtl .view-profile-icon {
          transform: translateX(10px);
          margin-left: 0;
          margin-right: 8px;
        }

        .rtl .doctor-card-link:hover .view-profile-icon {
          transform: translateX(0);
        }

        .doctor-card-link .image-box .image img {
          transition: all 0.3s ease;
        }

        .doctor-card-link:hover .image-box .image img {
          transform: scale(1.05);
        }

        /* RTL Support */
        .rtl .doctor-card-link:hover .inner-box {
          transform: translateY(-5px);
        }
      `}</style>
    </section>
  );
};

