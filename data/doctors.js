// Centralized doctor data for the medical center
export const doctorsData = {
  'dr-mawada': {
    id: 'dr-mawada',
    name: 'Dr. <PERSON>',
    roleKey: 'cosmetologist',
    image: '/assets/images/team/dr-mawada.png',
    experience: '8',
    email: '<EMAIL>',
    phone: '+971 7 123 4567',
    education: [
      {
        degree: 'MD in Dermatology',
        institution: 'University of Dubai',
        year: '2016'
      },
      {
        degree: 'Fellowship in Cosmetic Medicine',
        institution: 'American Academy of Cosmetic Medicine',
        year: '2018'
      }
    ],
    specializations: [
      'Botox & Fillers',
      'Laser Treatments',
      'Skin Rejuvenation',
      'Anti-aging Treatments'
    ],
    languages: ['Arabic', 'English', 'French'],
    workingHours: {
      sunday: '10:00 AM - 6:00 PM',
      monday: '10:00 AM - 6:00 PM',
      tuesday: '10:00 AM - 6:00 PM',
      wednesday: '10:00 AM - 6:00 PM',
      thursday: '10:00 AM - 6:00 PM',
      friday: 'Closed',
      saturday: '2:00 PM - 8:00 PM'
    },
    skills: {
      patientCare: 95,
      medicalExpertise: 90,
      communication: 88,
      problemSolving: 92
    }
  },
  'dr-samir': {
    id: 'dr-samir',
    name: 'Dr. Samir',
    roleKey: 'dermatologist',
    image: '/assets/images/team/dr-samir.png',
    experience: '12',
    email: '<EMAIL>',
    phone: '+971 7 123 4568',
    education: [
      {
        degree: 'MD in Dermatology',
        institution: 'Cairo University',
        year: '2012'
      },
      {
        degree: 'Board Certification in Dermatology',
        institution: 'Arab Board of Medical Specializations',
        year: '2015'
      }
    ],
    specializations: [
      'Skin Diseases',
      'Cosmetic Dermatology',
      'Hair Restoration',
      'Laser Therapy'
    ],
    languages: ['Arabic', 'English'],
    workingHours: {
      sunday: '9:00 AM - 5:00 PM',
      monday: '9:00 AM - 5:00 PM',
      tuesday: '9:00 AM - 5:00 PM',
      wednesday: '9:00 AM - 5:00 PM',
      thursday: '9:00 AM - 5:00 PM',
      friday: 'Closed',
      saturday: '1:00 PM - 7:00 PM'
    },
    skills: {
      patientCare: 93,
      medicalExpertise: 95,
      communication: 90,
      problemSolving: 94
    }
  },
  'dr-sultan': {
    id: 'dr-sultan',
    name: 'Dr. Sultan',
    roleKey: 'dentist',
    image: '/assets/images/team/dr-sultan.png',
    experience: '10',
    email: '<EMAIL>',
    phone: '+971 7 123 4569',
    education: [
      {
        degree: 'DDS - Doctor of Dental Surgery',
        institution: 'Jordan University of Science and Technology',
        year: '2014'
      },
      {
        degree: 'Certificate in Cosmetic Dentistry',
        institution: 'American Academy of Cosmetic Dentistry',
        year: '2017'
      }
    ],
    specializations: [
      'Cosmetic Dentistry',
      'Teeth Whitening',
      'Dental Implants',
      'Orthodontics'
    ],
    languages: ['Arabic', 'English'],
    workingHours: {
      sunday: '8:00 AM - 4:00 PM',
      monday: '8:00 AM - 4:00 PM',
      tuesday: '8:00 AM - 4:00 PM',
      wednesday: '8:00 AM - 4:00 PM',
      thursday: '8:00 AM - 4:00 PM',
      friday: 'Closed',
      saturday: '10:00 AM - 6:00 PM'
    },
    skills: {
      patientCare: 96,
      medicalExpertise: 89,
      communication: 92,
      problemSolving: 88
    }
  },
  'dr-tarek': {
    id: 'dr-tarek',
    name: 'Dr. Tarek',
    roleKey: 'trichologist',
    image: '/assets/images/team/dr-tarek.png',
    experience: '15',
    email: '<EMAIL>',
    phone: '+971 7 123 4570',
    education: [
      {
        degree: 'MD in Dermatology',
        institution: 'Alexandria University',
        year: '2009'
      },
      {
        degree: 'Diploma in Trichology',
        institution: 'International Association of Trichologists',
        year: '2012'
      }
    ],
    specializations: [
      'Hair Loss Treatment',
      'Hair Transplantation',
      'Scalp Disorders',
      'PRP Therapy'
    ],
    languages: ['Arabic', 'English', 'German'],
    workingHours: {
      sunday: '11:00 AM - 7:00 PM',
      monday: '11:00 AM - 7:00 PM',
      tuesday: '11:00 AM - 7:00 PM',
      wednesday: '11:00 AM - 7:00 PM',
      thursday: '11:00 AM - 7:00 PM',
      friday: 'Closed',
      saturday: '3:00 PM - 9:00 PM'
    },
    skills: {
      patientCare: 94,
      medicalExpertise: 97,
      communication: 89,
      problemSolving: 95
    }
  }
};

// Helper function to get all doctors as an array
export const getAllDoctors = () => {
  return Object.values(doctorsData);
};

// Helper function to get a doctor by ID
export const getDoctorById = (id) => {
  return doctorsData[id] || null;
};

// Helper function to get doctors by specialization
export const getDoctorsBySpecialization = (specialization) => {
  return Object.values(doctorsData).filter(doctor =>
    doctor.specializations.some(spec =>
      spec.toLowerCase().includes(specialization.toLowerCase())
    )
  );
};
