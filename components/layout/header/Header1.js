"use client";
import Link from "next/link";
import Menu from "../Menu";
import MobileMenu from "../MobileMenu";

import { useTranslation } from 'react-i18next';

export default function Header1({
  scroll,
  isMobileMenu,
  handleMobileMenu,
  isSidebar,
  handlePopup,
  handleSidebar,
}) {
  const { t } = useTranslation('common');

  return (
    <>
      <header className={`main-header ${scroll ? "fixed-header" : ""}`}>
        {/* Header Top */}
        <div className="header-top">
          <div className="auto-container">
            <div className="top-inner">
              <ul className="info-list clearfix">
                <li className="">
                  <i className="icon-1"></i>{t('header.openHours')}
                </li>
                <li>
                  <i className="icon-2"></i>{t('header.phone')}:{" "}
                  <Link href="tel:0556611307">0556611307</Link>
                </li>
                <li>
                  <Link href={'https://g.co/kgs/Vgwm2eb'}>
                    <img src="/assets/images/icons/icon-1.png" alt="" />  {t('header.location')}
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Header Upper */}
        <div className="header-lower">
          <div className="outer-container">
            <div className="auto-container">
              <div className="outer-box">
                <div className="logo-box">
                  <figure className="logo">
                    <Link href="/">
                      <img src="/assets/images/logo.png" alt="" width={100} height={30} className="logo-img" />
                    </Link>
                  </figure>
                </div>
                <div className="header-container-2">
                  <div className="menu-area">
                    <div
                      className="mobile-nav-toggler"
                      onClick={handleMobileMenu}
                    >
                      <i className="icon-bar"></i>
                      <i className="icon-bar"></i>
                      <i className="icon-bar"></i>
                    </div>
                    <nav className="main-menu navbar-expand-md navbar-light clearfix">
                      <div
                        className="collapse navbar-collapse show clearfix"
                        id="navbarSupportedContent"
                      >
                        <Menu />
                      </div>
                    </nav>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sticky Header  */}
        <div className="sticky-header">
          <div className="auto-container">
            <div className="outer-box">
              <div className="logo-box">
                <figure className="logo">
                  <Link href="/">
                    <img src="/assets/images/logo.png" width={100} height={30} alt="" />
                  </Link>
                </figure>
              </div>

              <nav className="main-menu navbar-expand-md navbar-light clearfix">
                <div
                  className="collapse navbar-collapse show clearfix"
                  id="navbarSupportedContent"
                >
                  <Menu />
                </div>
              </nav>
            </div>
          </div>
        </div>
        <MobileMenu handleMobileMenu={handleMobileMenu} />
      </header >
    </>
  );
}
