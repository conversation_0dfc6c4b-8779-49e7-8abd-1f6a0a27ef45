'use client'
import Link from "next/link";
import { useState } from "react";
import LanguageSwitcher from "@/components/elements/LanguageSwitcher";

export default function MobileMenu({ isSidebar, handleMobileMenu, handleSidebar }) {
  const [isActive, setIsActive] = useState({
    status: false,
    key: "",
    subMenuKey: "",
  });

  const handleToggle = (key, subMenuKey = "") => {
    if (isActive.key === key && isActive.subMenuKey === subMenuKey) {
      setIsActive({
        status: false,
        key: "",
        subMenuKey: "",
      });
    } else {
      setIsActive({
        status: true,
        key,
        subMenuKey,
      });
    }
  };

  return (
    <>
      <div className="mobile-menu">
        <div className="menu-backdrop" onClick={handleMobileMenu} />
        <div className="close-btn" onClick={handleMobileMenu}><span className="far fa-times" /></div>
        <nav className="menu-box">
          <div className="nav-logo">
            <Link href="/">
              <img src="/assets/images/logo.png" alt="" height={30} width={120} />
            </Link>
          </div>
          <div className="menu-outer">
            <div
              className="collapse navbar-collapse show clearfix"
              id="navbarSupportedContent">
              <ul className="navigation clearfix">
                <li className={isActive.key == 1 ? "dropdown current" : "dropdown"}>
                  <Link href="/" onClick={handleMobileMenu} >Home</Link>
                  {/* <Link><Link href="/"onClick={handleMobileMenu}>Home Page One</Link></li> */}

                  {/* <div className={isActive.key == 1 ? "dropdown-btn open" : "dropdown-btn"} onClick={() => handleToggle(1)}><span className="fa fa-angle-right" /></div> */}
                </li>
                <li><Link href="#about-us" className="menu-item" onClick={handleMobileMenu}>About</Link></li>
                <li className={isActive.key == 2 ? "dropdown current" : "dropdown"}>
                  <Link href="/#services" onClick={handleMobileMenu}>Services</Link>
                  {/* <ul style={{ display: `${isActive.key == 2 ? "block" : "none"}` }}>
                    <li><Link href="/service" onClick={handleMobileMenu}>Our Services</Link></li>
                    <li><Link href="/service-details" onClick={handleMobileMenu}>Cardioligy</Link></li>
                    <li><Link href="/service-details-2" onClick={handleMobileMenu}>Dental Clinic</Link></li>
                    <li><Link href="/service-details-3" onClick={handleMobileMenu}>NeuroSergery</Link></li>
                    <li><Link href="/service-details-4" onClick={handleMobileMenu}>Medical</Link></li>
                    <li><Link href="/service-details-5" onClick={handleMobileMenu}>Pediatrics</Link></li>
                    <li><Link href="/service-details-6" onClick={handleMobileMenu}>Modern Laboratory</Link></li>
                  </ul>
                  <div className={isActive.key == 2 ? "dropdown-btn open" : "dropdown-btn"} onClick={() => handleToggle(2)}><span className="fa fa-angle-right" /></div> */}
                </li>

                <li className={isActive.key == 3 ? "dropdown current" : "dropdown"}>
                  <Link href="/#team" onClick={handleMobileMenu}>Team</Link>

                </li>
                <li>
                  <Link href="#appointment" onClick={handleMobileMenu}>Appointment</Link>
                </li>
                <li className="language-switcher-mobile">
                  <LanguageSwitcher />
                </li>
              </ul>
            </div>
          </div>
          <div className="contact-info">
            <h4>Contact Info</h4>
            <ul>
              <li>khuzam road Lamp roundabout Opposit to nesto mall Building no.2 first floor</li>
              <li><Link href="tel:0556611307">0556611307
              </Link></li>
              <li><Link href="mailto:<EMAIL>"><EMAIL></Link></li>
            </ul>
          </div>
          {/*Social Links*/}
          <div className="social-links">
            <ul className="clearfix">

              <li><Link target="_blanc" href="https://www.instagram.com/zainmc.ae"><span className="fab fa-instagram"></span></Link></li>

            </ul>
          </div>
        </nav>
      </div>{/* End Mobile Menu */}
      <div className="nav-overlay" style={{ display: `${isSidebar ? "block" : "none"}` }} onClick={handleSidebar} />
    </>
  );
};

