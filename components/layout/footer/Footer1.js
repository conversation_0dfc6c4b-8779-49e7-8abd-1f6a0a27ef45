'use client';
import Link from "next/link"
import { useTranslation } from 'react-i18next';

export default function Footer1() {
  const { t } = useTranslation('common');
  return (
    <>
      <footer className="main-footer" id="footer">
        <div className="pattern-layer">
          <div className="pattern-1" style={{ backgroundImage: "url(assets/images/shape/shape-23.png)" }}></div>
          <div className="pattern-2" style={{ backgroundImage: "url(assets/images/shape/shape-24.png)" }}></div>
          <div className="pattern-3" style={{ backgroundImage: "url(assets/images/shape/shape-25.png)" }}></div>
          {/* <div className="pattern-4"></div> */}
        </div>
        <div className="widget-section pt_120 pb_100">
          <div className="auto-container">
            <div className="row clearfix">
              <div className="col-lg-3 col-md-6 col-sm-12 footer-column">
                <div className="footer-widget logo-widget">
                  <figure className="footer-logo"><Link href="/"><img src="assets/images/logo.png" alt="" width={130} /></Link></figure>
                  {/* <p>Lorem ipsum dolor sit amet constetur adipiscing elit. Etiam eu turpis mostie dictum est a, mattis tellus.</p> */}
                  <ul className="social-links clearfix">
                    <li><Link href="https://www.instagram.com/zainmc.ae/reels/"><i className="icon-4"></i></Link></li>
                  </ul>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-12 footer-column">
                <div className="footer-widget links-widget ml_110">
                  <div className="widget-title">
                    <h3>{t('footer.quickLinks')}</h3>
                  </div>
                  <div className="widget-content">
                    <ul className="links-list clearfix">
                      <li><Link href="/">{t('footer.links.home')}</Link></li>
                      <li><Link href="about">{t('footer.links.aboutUs')}</Link></li>
                      <li><Link href="service">{t('footer.links.services')}</Link></li>
                      <li><Link href="/">{t('footer.links.elements')}</Link></li>
                      <li><Link href="contact">{t('footer.links.contactUs')}</Link></li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-12 footer-column">
                <div className="footer-widget links-widget ml_55">
                  <div className="widget-title">
                    <h3>{t('footer.usefulLinks')}</h3>
                  </div>
                  <div className="widget-content">
                    <ul className="links-list clearfix">
                      <li><Link href="/">{t('footer.links.privacyPolicy')}</Link></li>
                      <li><Link href="/">{t('footer.links.terms')}</Link></li>
                      <li><Link href="/">{t('footer.links.condition')}</Link></li>
                      <li><Link href="/">{t('footer.links.support')}</Link></li>
                      <li><Link href="/">{t('footer.links.disclaimer')}</Link></li>
                      <li><Link href="/">{t('footer.links.faq')}</Link></li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-12 footer-column">
                <div className="footer-widget contact-widget">
                  <div className="widget-title">
                    <h3>{t('footer.contactUs')}</h3>
                  </div>
                  <div className="widget-content">
                    <ul className="info-list">
                      <li><img src="assets/images/icons/icon-1.png" alt="" />{t('header.location')}</li>
                      <li><i className="icon-2"></i><Link href="tel:0556611307">0556611307</Link></li>
                      <li><i className="icon-26"></i><Link href="mailto:<EMAIL>"><EMAIL></Link></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="footer-bottom">
          <div className="auto-container">
            <div className="bottom-inner">
              <ul className="footer-nav clearfix">
                <li><Link href="/">{t('footer.links.privacyPolicy')}</Link></li>
                <li><Link href="/">{t('footer.links.termsOfUse')}</Link></li>
                <li><Link href="/">{t('footer.links.salesAndRefunds')}</Link></li>
                <li><Link href="/">{t('footer.links.legal')}</Link></li>
                <li><Link href="/">{t('footer.links.siteMap')}</Link></li>
              </ul>
              <div className="copyright">
                <p>{t('footer.copyright')}</p>
              </div>
            </div>
          </div>
        </div>
      </footer>

    </>
  )
}
