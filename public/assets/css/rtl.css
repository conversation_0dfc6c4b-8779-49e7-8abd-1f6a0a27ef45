.rtl {
  direction: rtl;
}

.ltr {
  direction: ltr;
}

.rtl .switcher {
  right: 60px;
  left: inherit;
}

.rtl .switcher .switch_menu {
  left: inherit;
  right: 0px;
}

.rtl .owl-carousel {
  direction: ltr;
}

.rtl .scroll-to-top .scroll-bar {
  margin-right: 0px;
  margin-left: 10px;
}

.navigation {
  align-items: center;
}

.rtl .main-menu .navigation>li {
  float: right;
}

.rtl .main-menu .navigation>li:first-child {
  margin-left: 15px !important;
  margin-right: 0px;
}

.rtl .main-menu .navigation>li:last-child {
  margin-right: 15px !important;
  margin-left: 0px;
}

.rtl .main-menu .navigation>li>ul>li>a,
.rtl .main-menu .navigation>li>.megamenu li>a {
  text-align: right;
}

.rtl .main-menu .navigation li.dropdown .megamenu li h4 {
  text-align: right;
}

.rtl .main-menu .navigation>li>ul>li>ul>li>a {
  text-align: right;
}

.rtl .main-menu .navigation>li>ul>li.dropdown>a:after {
  right: inherit;
  left: 20px;
  content: "\f104";
}

.rtl .mobile-menu {
  text-align: right;
}

.rtl .mobile-menu .nav-logo {
  text-align: right;
}

.rtl .mobile-menu .navigation li>a:before {
  left: inherit;
  right: 0px;
}

.rtl .mobile-menu .navigation li.dropdown .dropdown-btn {
  right: inherit;
  left: 6px;
}

.rtl .mobile-menu .navigation li.dropdown .dropdown-btn.open {
  transform: rotate(-90deg);
}

.rtl .scroll-to-top {
  right: inherit;
  left: 0px;
}

.rtl .header-top .top-inner .info-list li {
  margin-right: 0px;
  margin-left: 50px;
}



.rtl .list-style-one li {
  padding-left: 0px !important;
  padding-right: 40px !important;
  text-align: right;
}

.rtl .list-style-one li:before {
  left: inherit !important;
  right: 0px !important;
}

/* Specific fix for About section services list */
.rtl .content_block_one .content-box .list-style-one li {
  float: right !important;
  padding-left: 0px !important;
  padding-right: 40px !important;
  text-align: right !important;
}

.rtl .content_block_one .content-box .list-style-one li:before {
  left: inherit !important;
  right: 0px !important;
}

.rtl .footer-widget.ml_110 {
  margin-left: 0px;
  margin-right: 110px;
}

.rtl .footer-widget.ml_55 {
  margin-left: 0px;
  margin-right: 55px;
}

/* RTL Section Headers Alignment */
.rtl .sec-title {
  text-align: right;
}

.rtl .sec-title .sub-title {
  text-align: right;
}

.rtl .sec-title h2 {
  text-align: right;
}

/* RTL Centered Section Headers - keep centered when explicitly centered */
.rtl .sec-title.centred {
  text-align: center;
}

.rtl .sec-title.centred .sub-title {
  text-align: center;
}

.rtl .sec-title.centred h2 {
  text-align: center;
}

/* RTL Content Alignment */
.rtl .content_block_one .content-box {
  text-align: right;
}

.rtl .content_block_one .content-box .text-box {
  text-align: right;
}

.rtl .content_block_one .content-box .text-box p {
  text-align: right;
}

/* RTL Service Block Content */
.rtl .service-block-one .inner-box .lower-content {
  text-align: right;
}

.rtl .service-block-one .inner-box .lower-content h3 {
  text-align: right;
}

.rtl .service-block-one .inner-box .lower-content p {
  text-align: right;
}

/* RTL Video Section Header - Only apply in RTL mode */
.rtl .video-section .inner-box {
  text-align: right;
}

.rtl .video-section .inner-box h2 {
  text-align: right;
}

.rtl .main-footer .contact-widget .info-list li {
  padding-left: 0px;
  padding-right: 30px;
}

.rtl .main-footer .contact-widget .info-list li img {
  left: inherit;
  right: 0px;
}

.rtl .main-footer .contact-widget .info-list li i {
  left: inherit;
  right: 0px;
}

.rtl .main-footer .logo-widget .social-links li {
  margin-right: 0px;
  margin-left: 20px;
}

.rtl .progress-box .count-text {
  right: inherit;
  left: 0px;
}