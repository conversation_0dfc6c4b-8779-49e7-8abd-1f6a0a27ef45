'use client';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function service() {
  const { t } = useTranslation('common');
  return (
    <section className="service-section sec-pad" id="services">
      <div className="auto-container">
        <div className="sec-title mb_50 centred">
          <span className="sub-title">{t('services.subtitle')}</span>
          <h2>{t('services.mainTitle')}</h2>
        </div>
        <div className="row clearfix">
          <div className="col-lg-4 col-md-6 col-sm-12 service-block">
            <div className="service-block-one wow fadeInUp animated" data-wow-delay="00ms" data-wow-duration="1500ms">
              <div className="inner-box">
                <div className="image-box">
                  <figure className="image"><Link href="service-details-3"><img src="assets/images/service/dental-clinic.jpg" alt="" /></Link></figure>
                  <div className="icon-box"><Image width={52} height={72} alt='dental' src='/assets/images/icons/dental-icon-2.svg' /></div>
                </div>
                <div className="lower-content">
                  <h3>{t('services.dental.title')}</h3>
                  <p>{t('services.dental.description')}</p>
                </div>
              </div>
            </div>
          </div>
          <div className="col-lg-4 col-md-6 col-sm-12 service-block">
            <div className="service-block-one wow fadeInUp animated" data-wow-delay="300ms" data-wow-duration="1500ms">
              <div className="inner-box">
                <div className="image-box">
                  <figure className="image"><Link href="service-details-6"><img src="assets/images/service/laser-clinic.jpg" alt="" /></Link></figure>
                  <div className="icon-box"><i className="icon-16"></i></div>
                </div>
                <div className="lower-content">
                  <h3><Link href="service-details-6">{t('services.cosmetic.title')}</Link></h3>
                  <p>{t('services.cosmetic.description')}</p>
                </div>
              </div>
            </div>
          </div>
          <div className="col-lg-4 col-md-6 col-sm-12 service-block">
            <div className="service-block-one wow fadeInUp animated" data-wow-delay="600ms" data-wow-duration="1500ms">
              <div className="inner-box">
                <div className="image-box">
                  <figure className="image"><Link href="service-details"><img src="assets/images/service/hair-clinic.jpg" alt="" /></Link></figure>
                  <div className="icon-box"><i className="icon-17"></i></div>
                </div>
                <div className="lower-content">
                  <h3><Link href="service-details">{t('services.dermatology.title')}</Link></h3>
                  <p>{t('services.dermatology.description')}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
