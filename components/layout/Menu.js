"use client";
import Link from "next/link";
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from "@/components/elements/LanguageSwitcher";
// import { useRouter } from "next/router"

export default function Menu() {
  // const router = useRouter()
  const { t } = useTranslation('common');

  return (
    <>
      <ul className="navigation clearfix">
        <li className="dropdown">
          <Link href="/">{t('navigation.home')}</Link>
        </li>
        <li>
          <Link href="#about-us">{t('navigation.aboutUs')}</Link>
        </li>

        <li>
          <Link href="#serviceS">{t('navigation.ourServices')}</Link>
        </li>


        <li>
          <Link href="#team">{t('navigation.ourTeam')}</Link>
        </li>
        <li>
          <Link href="#appointment">{t('navigation.appointment')}</Link>
        </li>

        <li className="language-switcher-nav">
          <LanguageSwitcher />
        </li>

        {/* Pages */}
        {/*  */}
        {/* Contact */}

      </ul>
    </>
  );
}
