


'use client'
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function VideoGallery() {
  const { t } = useTranslation('common');
  const videos = [
    {
      id: 1,
      url: "/videos/video-1.mp4", // Replace with your video path

    },
    {
      id: 2,
      url: "/videos/video-2.mp4",

    },
    {
      id: 3,
      url: "/videos/video-3.mp4",

    }
  ];

  return (
    <section className="video-section p_relative">
      <div className="bg-layer parallax-bg" data-parallax='{"y": 100}'
        style={{ backgroundColor: "#6E7086" }}></div>

      <div className="auto-container">
        <div className="inner-box">
          {/* <div className="shape" style={{ backgroundImage: 'url(assets/images/shape/shape-17.png)' }}></div> */}
          <h2>{t('video.title')}</h2>
          <div className="instagram-posts-grid" >
            {videos.map((video) => (
              <div key={video.id} className="custom-instagram-post" style={{
                background: '#fff',
                borderRadius: '15px',
                boxShadow: '0 4px 24px rgba(0,0,0,0.1)',
                overflow: 'hidden',
                position: 'relative'

              }}>
                {/* Video Container */}
                <div className="video-container" style={{
                  position: 'relative',
                  paddingTop: '100%',
                  backgroundColor: '#000'
                }}>
                  <video
                    controls
                    autoPlay={video.id == 1 && true}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      borderRadius: '15px 15px 0 0'
                    }}
                    poster={video.id === 1 && "/assets/images/team/dr-sultan.png"} // Add thumbnail image
                  >
                    <source src={video.url} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>


                </div>


                <div className="post-footer" style={{
                  padding: '12px 15px',
                  borderTop: '1px solid #efefef',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px'
                }}>
                  <div className="user-avatar" style={{
                    width: '30px',
                    height: '30px',
                    borderRadius: '50%',
                    backgroundColor: '#f4f4f4'
                  }}></div>
                  <span style={{
                    fontSize: '12px',
                    color: '#8e8e8e',
                    fontWeight: '500'
                  }}>@zainmc.ae</span>
                </div>
              </div>
            ))}
          </div>

          {/* <div className="btn-box" style={{ marginTop: '40px', textAlign: 'center' }}>
            <Link href="#appointment" className="theme-btn btn-one">
              <span>Make an Appointment</span>
            </Link>
          </div> */}
        </div>
      </div>
    </section>
  );
}