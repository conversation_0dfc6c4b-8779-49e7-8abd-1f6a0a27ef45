
/** video-section **/

.video-section{
  position: relative;
  overflow: hidden;
  padding: 145px 0px;
}

.video-section .bg-layer:before{
  position: absolute;
  content: '';
  background: #0E1136;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  opacity: 0.6;
}

.video-section .inner-box{
  position: relative;
  display: block;
  max-width: 1090px;
}

.video-section .inner-box .video-btn{
  position: absolute;
  top: 85px;
  right: 0px;
  cursor:pointer
}

.video-section .inner-box .video-btn a{
  position: relative;
  display: inline-block;
  width: 100px;
  height: 100px;
  line-height: 100px;
  font-size: 40px;
  color: #fff;
  text-align: center;
  border-radius: 50%;
  background: var(--theme-color);
  z-index: 1;
}

.video-section .inner-box h2{
  display: block;
  font-size: 64px;
  line-height: 75px;
  color: #fff;
  font-weight: 700;
  margin-bottom: 50px;
}

.video-section .inner-box .shape{
  position: absolute;
  right: 185px;
  bottom: 0px;
  width: 278px;
  height: 139px;
  background-repeat: no-repeat;
}

.video-section .image-layer{
  position: absolute;
  right: 60px;
  bottom: 0px;
}

.video-section.alternat-2{
  overflow: visible;
}

.video-section.alternat-2 .bg-layer{
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: left center;
}





/** RTL-CSS **/

.rtl .video-section .inner-box .video-btn{
  right: inherit;
  left: 0px;
}



/** RESPONSIVE-CSS **/


@media only screen and (max-width: 1200px){

  .video-section .image-layer{
    display: none;
  }

}

@media only screen and (max-width: 991px){

  .video-section .inner-box .video-btn{
    position: relative;
    top: 0px;
    margin-bottom: 40px;
  }

  .video-section .inner-box h2 br{
    display: none;
  }

}

@media only screen and (max-width: 767px){

  .video-section .inner-box h2{
    font-size: 36px;
    line-height: 46px;
  }

  .video-section .inner-box .shape{
    display: none;
  }

  .video-section{
    padding: 90px 0px 70px 0px;
  }

}

@media only screen and (max-width: 599px){

}

@media only screen and (max-width: 499px){


}












































