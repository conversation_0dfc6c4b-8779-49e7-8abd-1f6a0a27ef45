
/** news-section **/

.news-section{
  position: relative;
}

.news-block-one .inner-box{
  position: relative;
  display: block;
  background: #FFFFFF;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 30px;
}

.news-block-one .inner-box .image-box{
  position: relative;
  display: block;
  overflow: hidden;
}

.news-block-one .inner-box .image-box img{
  width: 100%;
  transition: all 500ms ease;
}

.news-block-one .inner-box:hover .image-box img{
  transform: scale(1.05);
}

.news-block-one .inner-box .lower-content{
  position: relative;
  display: block;
  padding: 30px;
  border: 1px solid rgba(14, 17, 54, 0.1);
  border-radius: 0px 0px 20px 20px;
  border-top: none;
}

.news-block-one .inner-box .lower-content .post-info li{
  position: relative;
  display: inline-block;
  float: left;
  font-size: 16px;
  line-height: 26px;
  font-weight: 500;
  margin-right: 40px;
}

.news-block-one .inner-box .lower-content .post-info li:last-child{
  margin: 0px !important;
}

.news-block-one .inner-box .lower-content .post-info li a{
  display: inline-block;
  color: #676767;
}

.news-block-one .inner-box .lower-content .post-info li a:hover{
  color: var(--secondary-color);
}

.news-block-one .inner-box .lower-content .post-info li:before{
  position: absolute;
  content: '';
  background: rgba(103, 103, 103, 1);
  width: 1px;
  height: 10px;
  top: 8px;
  right: -20px;
}

.news-block-one .inner-box .lower-content .post-info li:last-child:before{
  display: none;
}

.news-block-one .inner-box .lower-content h3{
  display: block;
  font-size: 26px;
  line-height: 36px;
  font-weight: 600;
  margin-bottom: 15px;
}

.news-block-one .inner-box .lower-content h3 a{
  display: inline-block;
  color: var(--title-color);
}

.news-block-one .inner-box .lower-content h3 a:hover{
  color: var(--theme-color);
}

.news-block-one .inner-box .lower-content p{
  margin-bottom: 30px;
}

.news-block-one .inner-box .lower-content .link a{
  position: relative;
  display: inline-block;
  font-size: 16px;
  line-height: 26px;
  color: #676767;
  font-weight: 500;
  text-transform: uppercase;
}

.news-block-one .inner-box .lower-content .link a:hover{
  color: var(--secondary-color);
}

.news-block-one .inner-box .lower-content .link a span{
  position: relative;
  padding-right: 25px;
}

.news-block-one .inner-box .lower-content .link a span:before{
  position: absolute;
  content: '\e918';
  font-family: 'icomoon';
  font-size: 18px;
  top: -1px;
  right: 0px;
  font-weight: 400;
}

.blog-classic-content{
  position: relative;
  display: block;
}

.blog-classic-content .news-block-one .inner-box{
  padding-left: 416px;
}

.blog-classic-content .news-block-one .inner-box .image-box{
  position: absolute;
  left: 0px;
  top: 0px;
}

.blog-classic-content .news-block-one .inner-box .lower-content{
  border: 1px solid rgba(14, 17, 54, 0.1);
  border-radius: 0px 20px 20px 0px;
  border-left: none;
  padding: 37px 40px;
}





/** RTL-CSS **/

.rtl .news-block-one .inner-box .lower-content .post-info li{
  float: right;
  margin-right: 0px;
  margin-left: 40px;
}

.rtl .news-block-one .inner-box .lower-content .post-info li:before{
  right: inherit;
  left: -20px;
}



/** RESPONSIVE-CSS **/


@media only screen and (max-width: 1200px){

  .blog-classic-content .news-block-one .inner-box{
    padding-left: 0px;
  }

  .blog-classic-content .news-block-one .inner-box .image-box{
    position: relative;
    max-width: 416px;
    margin-bottom: 30px;
    border-radius: 20px;
  }

  .blog-classic-content .news-block-one .inner-box .image-box img{
    width: 100%;
    border-radius: 20px;
  }

  .blog-classic-content .news-block-one .inner-box .lower-content {
    border: 1px solid rgba(14, 17, 54, 0.1);
    border-radius: 20px;
  }

}

@media only screen and (max-width: 991px){

  

}

@media only screen and (max-width: 767px){

  

}

@media only screen and (max-width: 599px){

}

@media only screen and (max-width: 499px){


}












































