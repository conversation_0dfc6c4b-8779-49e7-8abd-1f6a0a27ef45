'use client'

import { useTranslation } from 'react-i18next'
import { useEffect, useState } from 'react'

export default function LanguageSwitcher() {
  const { i18n, t } = useTranslation('common')
  const [isOpen, setIsOpen] = useState(false)

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng)
    setIsOpen(false) // Close dropdown after selection

    // Store language preference
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', lng)

      // Update document direction and lang attribute
      if (lng === 'ar') {
        document.documentElement.setAttribute('dir', 'rtl')
        document.documentElement.setAttribute('lang', 'ar')
        document.body.classList.add('rtl')
      } else {
        document.documentElement.setAttribute('dir', 'ltr')
        document.documentElement.setAttribute('lang', 'en')
        document.body.classList.remove('rtl')
      }
    }
  }

  useEffect(() => {
    // Load saved language preference
    if (typeof window !== 'undefined') {
      const savedLang = localStorage.getItem('language') || 'en'
      if (savedLang !== i18n.language) {
        i18n.changeLanguage(savedLang)
      }

      // Set initial direction based on current language
      const currentLang = i18n.language || savedLang
      if (currentLang === 'ar') {
        document.documentElement.setAttribute('dir', 'rtl')
        document.documentElement.setAttribute('lang', 'ar')
        document.body.classList.add('rtl')
      } else {
        document.documentElement.setAttribute('dir', 'ltr')
        document.documentElement.setAttribute('lang', 'en')
        document.body.classList.remove('rtl')
      }
    }
  }, [i18n.language])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.language-switcher-wrapper')) {
        setIsOpen(false)
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [])

  return (
    <div className="language-switcher-wrapper">
      <button
        className="language-nav-link"
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <i className="fas fa-globe" style={{ marginRight: '8px' }}></i>
        {i18n.language === 'ar' ? 'العربية' : 'English'}
      </button>
      {isOpen && (
        <ul className="language-dropdown">
          <li>
            <button
              className={`language-dropdown-item ${i18n.language === 'en' ? 'active' : ''}`}
              onClick={() => changeLanguage('en')}
            >
              <span className="flag-icon">🇺🇸</span>
              {t('language.english')}
            </button>
          </li>
          <li>
            <button
              className={`language-dropdown-item ${i18n.language === 'ar' ? 'active' : ''}`}
              onClick={() => changeLanguage('ar')}
            >
              <span className="flag-icon">🇦🇪</span>
              {t('language.arabic')}
            </button>
          </li>
        </ul>
      )}

      <style jsx>{`
        /* Language switcher wrapper */
        .language-switcher-wrapper {
          position: relative;
        }

        /* Main navigation link styling - exactly like other nav items */
        .language-nav-link {
          position: relative;
          display: block;
          text-align: center;
          font-size: 20px;
          line-height: 26px;
          padding: 39px 0px 35px 0px;
          font-family: var(--soleil);
          letter-spacing: 0.8px;
          opacity: 1;
          color: #0e1136;
          z-index: 1;
          transition: all 500ms ease;
          border: none;
          background: transparent;
          cursor: pointer;
          text-decoration: none;
        }

        .language-nav-link:hover {
          color: var(--secondary-color);
        }

        .language-nav-link:before {
          position: absolute;
          content: "";
          background: rgba(249, 49, 59, 0.1);
          width: 39px;
          height: 39px;
          border-radius: 50%;
          left: -12px;
          top: 32px;
          transform: scale(0, 0);
          transition: all 500ms ease;
        }

        .language-nav-link:hover:before {
          transform: scale(1, 1);
        }

        /* Dropdown menu */
        .language-dropdown {
          position: absolute;
          left: inherit;
          top: 100%;
          width: 230px;
          margin-top: 15px;
          z-index: 100;
          background: #0e1136;
          box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
          border-radius: 0px;
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .language-dropdown li {
          width: 100%;
        }

        .language-dropdown-item {
          position: relative;
          display: block;
          padding: 10px 25px;
          line-height: 24px;
          font-size: 18px;
          text-transform: capitalize;
          font-family: var(--soleil);
          color: #fff;
          text-align: left;
          transition: all 500ms ease;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          border-left: none;
          border-right: none;
          border-top: none;
          background: none;
          width: 100%;
          cursor: pointer;
        }

        .language-dropdown-item:hover {
          padding-left: 35px;
          color: var(--secondary-color);
        }

        .language-dropdown li:last-child .language-dropdown-item {
          border-bottom: none;
        }

        .flag-icon {
          margin-right: 8px;
          font-size: 16px;
        }

        /* Sticky header adjustments */
        .sticky-header .language-nav-link {
          padding-top: 27px;
          padding-bottom: 27px;
        }

        .sticky-header .language-nav-link:before {
          top: 19px;
        }

        /* RTL specific styles */
        .rtl .language-dropdown {
          right: 0;
          left: auto;
        }

        .rtl .language-dropdown-item {
          text-align: right;
        }

        .rtl .flag-icon {
          margin-left: 8px;
          margin-right: 0;
        }

        /* Mobile menu styles - Match other mobile menu items exactly */
        .language-switcher-mobile {
          position: relative;
          display: block;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .language-switcher-mobile .language-switcher-wrapper {
          position: relative;
        }

        .language-switcher-mobile .language-nav-link {
          position: relative;
          display: block;
          line-height: 24px;
          padding: 10px 25px;
          font-size: 15px;
          font-weight: 500;
          color: #ffffff;
          text-transform: uppercase;
          transition: all 500ms ease;
          border: none;
          background: transparent;
          cursor: pointer;
          text-align: left;
          width: 100%;
        }

        .language-switcher-mobile .language-nav-link:before {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          height: 0;
          border-left: 5px solid #fff;
          transition: all 500ms ease;
        }

        .language-switcher-mobile .language-nav-link:hover:before {
          height: 100%;
        }

        .language-switcher-mobile .language-nav-link:hover {
          color: #ffffff;
        }

        .language-switcher-mobile .language-dropdown {
          position: static;
          width: 100%;
          margin-top: 0;
          background: rgba(255, 255, 255, 0.1);
          box-shadow: none;
          border-radius: 0;
        }

        .language-switcher-mobile .language-dropdown-item {
          padding: 8px 45px;
          font-size: 14px;
          color: #ffffff;
          text-align: left;
          border-bottom: 1px solid rgba(255, 255, 255, 0.05);
          text-transform: capitalize;
          font-weight: 400;
        }

        .language-switcher-mobile .language-dropdown-item:hover {
          padding-left: 55px;
          color: var(--secondary-color);
        }
      `}</style>
    </div>
  )
}
