
/** team-details **/

.team-details{
  position: relative;
}

.team-details-content{
  position: relative;
  display: block;
}

.team-details-content .image-box{
  position: relative;
  display: block;
  border-radius: 18px;
}

.team-details-content .image-box img{
  width: 100%;
  border-radius: 18px;
}

.team-details-content .content-box{
  position: relative;
  display: block;
}

.team-details h2{
  font-size: 36px;
  line-height: 46px;
  font-weight: 600;
  margin-bottom: 15px;
}

.team-details-content .content-box .designation{
  position: relative;
  display: block;
  font-size: 18px;
  line-height: 30px;
  color: var(--secondary-color);
  margin-bottom: 15px;
}

.team-details-content .content-box p{
  display: block;
  margin-bottom: 30px;
}

.team-details-content .content-box .info-list li{
  position: relative;
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.team-details-content .content-box .info-list li:last-child{
  margin-bottom: 0px;
}

.team-details-content .content-box .info-list li a{
  color: var(--text-color);
}

.team-details-content .content-box .info-list li a:hover{
  color: var(--secondary-color);
}

.team-details-content .content-box .info-list li strong{
  font-weight: 600;
  color: var(--title-color);
}

.team-details-content .content-box .social-links li{
  position: relative;
  display: inline-block;
  font-size: 18px;
  margin-right: 15px;
}

.team-details-content .content-box .social-links li:last-child{
  margin: 0px !important;
}

.team-details-content .content-box .social-links li a{
  display: inline-block;
  color: var(--title-color);
}

.team-details-content .content-box .social-links li a:hover{
  color: var(--secondary-color);
}

.team-details .experience-details h2{
  margin-bottom: 30px;
}

.team-details .experience-details p{
  margin-bottom: 25px;
}

.team-details .experience-details p:last-child{
  margin-bottom: 0px;
}

.team-details .skills-box h2{
  margin-bottom: 30px;
}

.team-details .skills-box .progress-inner{
  max-width: 550px;
}

.team-details .appointment-inner h2{
  margin-bottom: 30px;
}

.team-details .appointment-inner .form-group{
  margin-bottom: 30px;
}

.team-details .appointment-inner .form-group:last-child{
  margin-bottom: 0px;
}





/** RTL-CSS **/

.rtl .team-details-content .content-box .social-links li{
  margin-right: 0px;
  margin-left: 15px;
}



/** RESPONSIVE-CSS **/


@media only screen and (max-width: 1200px){



}

@media only screen and (max-width: 991px){

  .team-details-content .image-box{
    margin-right: 0px;
    margin-bottom: 30px;
  }

}

@media only screen and (max-width: 767px){

  .team-details .skills-box{
    margin-bottom: 30px;
  }

}

@media only screen and (max-width: 599px){

}

@media only screen and (max-width: 499px){


}












































