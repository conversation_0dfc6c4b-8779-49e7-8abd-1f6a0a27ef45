'use client';
import Layout from "@/components/layout/Layout"
import About from "@/components/sections/home1/About"
import Banner from "@/components/sections/home1/Banner"
import Services from "@/components/sections/home1/Services"
import Testimonial from "@/components/sections/home1/Testimonial"
import Team from "@/components/sections/home1/Team"
import Video from "@/components/sections/home1/Video"
import { useTranslation } from 'react-i18next';

export default function Home() {
    const { t } = useTranslation('common');

    return (
        <>
            <Layout headerStyle={1} footerStyle={1}>
                <Banner />
                {/* <Features /> */}
                <Team />
                <Services />
                <About />
                {/* <WhyChooseUs /> */}
                {/* <Funfacts /> */}
                <Video />
                <div id="appointment" style={{
                    width: '100%', height: '800px', marginBottom: '70px',
                    marginTop: '70px'
                }} className="auto-container">

                    <div className="sec-title mb_50">
                        <span className="sub-title">{t('navigation.appointment')}</span>
                        <h2>{t('buttons.getAppointment')}</h2>
                    </div>
                    <iframe
                        src="https://appointment-system-xi.vercel.app/"
                        width="100%"
                        height="100%"
                        style={{ border: "none" }}
                        allow="clipboard-write"
                        loading="lazy"
                        title="Appointment Booking"
                    />
                </div>
                {/* <Process /> */}
                <Testimonial />
                {/* <Pricing /> */}
                {/* <News /> */}
                {/* <Subscribe /> */}
            </Layout>
        </>
    )
}