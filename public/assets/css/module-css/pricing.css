
/** pricing-section **/

.pricing-section{
  position: relative;
}

.pricing-block-one .pricing-table{
  position: relative;
  display: block;
  background: #FFFFFF;
  border: 1px solid #E5E5E5;
  border-radius: 20px;
  padding: 50px;
  overflow: hidden;
  margin-bottom: 30px;
}

.pricing-block-one .pricing-table .table-header{
  position: relative;
  display: block;
  padding-bottom: 30px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e5e5e5;
}

.pricing-block-one .pricing-table .table-header h5{
  display: block;
  font-size: 18px;
  line-height: 30px;
  text-transform: uppercase;
  margin-bottom: 15px;
  color: var(--theme-color);
}

.pricing-block-one .pricing-table .table-header h2{
  display: block;
  font-size: 50px;
  line-height: 50px;
  font-weight: 600;
  margin-bottom: 15px;
}

.pricing-block-one .pricing-table .table-header span{
  position: relative;
  display: block;
  font-size: 18px;
  line-height: 30px;
  font-weight: 600;
}

.pricing-block-one .pricing-table .feature-list li{
  position: relative;
  display: block;
  font-size: 16px;
  line-height: 26px;
  font-weight: 500;
  padding-left: 28px;
  margin-bottom: 17px;
}

.pricing-block-one .pricing-table .feature-list li:last-child{
  margin-bottom: 0px;
}

.pricing-block-one .pricing-table .feature-list li:before{
  position: absolute;
  content: '\e917';
  font-family: 'icomoon';
  font-size: 18px;
  left: 0px;
  top: 0px;
  color: var(--secondary-color);
}

.pricing-block-one .pricing-table .feature-list{
  margin-bottom: 50px;
}

.pricing-block-one .pricing-table .shape{
  position: absolute;
  left: 0px;
  bottom: 40px;
  width: 100%;
  height: 231px;
  background-size: cover;
  background-repeat: no-repeat;
}

.pricing-block-one.active-block .pricing-table{
  border-color: #fff;
  box-shadow: 0px 2px 70px rgba(0, 0, 0, 0.1);
}

.pricing-block-one.active-block .pricing-table .table-header h5{
  color: var(--secondary-color) !important;
}

.pricing-block-one.active-block .pricing-table .table-header h2{
  color: var(--theme-color);
}

.pricing-block-one.active-block .pricing-table .theme-btn.btn-three{
  background: var(--theme-color);
  border-color: var(--theme-color);
  color: #fff !important;
}






/** RTL-CSS **/


.rtl .pricing-block-one .pricing-table .feature-list li{
  padding-left: 0px;
  padding-right: 28px;
}

.rtl .pricing-block-one .pricing-table .feature-list li:before{
  left: inherit;
  right: 0px;
}


/** RESPONSIVE-CSS **/


@media only screen and (max-width: 1200px){



}

@media only screen and (max-width: 991px){

  

}

@media only screen and (max-width: 767px){

  

}

@media only screen and (max-width: 599px){

}

@media only screen and (max-width: 499px){

  .pricing-block-one .pricing-table{
    padding-left: 30px;
    padding-right: 30px;
  }

}












































