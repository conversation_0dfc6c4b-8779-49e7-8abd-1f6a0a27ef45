
/** cta-section **/

.cta-section{
  position: relative;
  padding: 150px 0px 248px 0px;
  overflow: hidden;
}

.cta-section .bg-layer:before{
  position: absolute;
  content: '';
  background: #0E1136;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  opacity: 0.8;
}

.cta-section h2{
  font-size: 64px;
  line-height: 75px;
  font-weight: 700;
  margin-bottom: 50px;
}

.cta-section .inner-box{
  max-width: 990px;
}

.cta-section .image-layer{
  position: absolute;
  right: 200px;
  bottom: 0px;
}





/** RTL-CSS **/





/** RESPONSIVE-CSS **/


@media only screen and (max-width: 1200px){



}

@media only screen and (max-width: 991px){

  .cta-section .image-layer{
    display: none;
  }

}

@media only screen and (max-width: 767px){

  .cta-section{
    padding: 64px 0px 170px 0px;
  }

  .cta-section h2{
    font-size: 36px;
    line-height: 46px;
  }

}

@media only screen and (max-width: 599px){

}

@media only screen and (max-width: 499px){


}












































