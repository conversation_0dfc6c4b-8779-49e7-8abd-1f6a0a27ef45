'use client';
import React from 'react';
import { useParams } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import Layout from "@/components/layout/Layout";
import Link from "next/link";
import { getDoctorById } from '@/data/doctors';

const ProgressBar = ({ label, percent }) => (
  <div className="progress-box">
    <p>{label}</p>
    <div className="bar">
      <div className="bar-inner count-bar" style={{ width: `${percent}%` }}></div>
      <div className="count-text">{`${percent}%`}</div>
    </div>
  </div>
);

export default function DoctorDetails() {
  const params = useParams();
  const { t, i18n } = useTranslation('common');
  const doctorId = params.id;
  const doctor = getDoctorById(doctorId);

  if (!doctor) {
    return (
      <Layout headerStyle={2} footerStyle={1} breadcrumbTitle="Doctor Not Found">
        <section className="error-section sec-pad-2">
          <div className="auto-container">
            <div className="content-box centred">
              <h2>Doctor Not Found</h2>
              <p>The doctor you are looking for does not exist.</p>
              <Link href="/#team" className="theme-btn btn-one">
                <span>Back to Team</span>
              </Link>
            </div>
          </div>
        </section>
      </Layout>
    );
  }

  return (
    <Layout headerStyle={2} footerStyle={1} breadcrumbTitle={`${doctor.name} - ${t(`team.roles.${doctor.roleKey}`)}`}>
      <section className="team-details sec-pad-2">
        <div className="auto-container">
          <div className="team-details-content mb_50">
            <div className="row clearfix">
              <div className="col-lg-5 col-md-12 col-sm-12 image-column">
                <figure className="image-box mr_15">
                  <img src={doctor.image} alt={doctor.name} style={{ objectFit: 'cover', width: '100%', height: 'auto' }} />
                </figure>
              </div>
              <div className="col-lg-7 col-md-12 col-sm-12 content-column">
                <div className="content-box">
                  <h2>{doctor.name}</h2>
                  <span className="designation">{t(`team.roles.${doctor.roleKey}`)}</span>
                  <p>{t(`doctorDetails.${doctor.id}.description`)}</p>
                  <ul className="info-list mb_30 clearfix">
                    <li><strong>{t('medical.experience')}: </strong>{doctor.experience} {t('medical.years')}</li>
                    <li><strong>{t('contact.email')}: </strong><Link href={`mailto:${doctor.email}`}>{doctor.email}</Link></li>
                    <li><strong>{t('header.phone')}: </strong><Link href={`tel:${doctor.phone}`}>{doctor.phone}</Link></li>
                  </ul>
                  <div className="btn-box">
                    <Link href="/#appointment" className="theme-btn btn-one">
                      <span>{t('buttons.bookAppointment')}</span>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Education & Experience Section */}
          <div className="experience-details mb_50">
            <h2>{t('doctorDetails.education')}</h2>
            <div className="row clearfix">
              <div className="col-lg-6 col-md-12 col-sm-12">
                <h4>{t('doctorDetails.educationBackground')}</h4>
                <ul className="list-style-one clearfix">
                  {doctor.education.map((edu, index) => (
                    <li key={index}>
                      <strong>{edu.degree}</strong><br />
                      {edu.institution} ({edu.year})
                    </li>
                  ))}
                </ul>
              </div>
              <div className="col-lg-6 col-md-12 col-sm-12">
                <h4>{t('doctorDetails.specializations')}</h4>
                <ul className="list-style-one clearfix">
                  {doctor.specializations.map((spec, index) => (
                    <li key={index}>{spec}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Skills & Languages Section */}
          <div className="two-column mb_50">
            <div className="row clearfix">
              <div className="col-lg-6 col-md-6 col-sm-12 skills-column">
                <div className="skills-box">
                  <div className="text-box mb_30">
                    <h2>{t('doctorDetails.expertiseSkills')}</h2>
                    <p>{t(`doctorDetails.${doctor.id}.skillsDescription`)}</p>
                  </div>
                  <ProgressBar label={t('doctorDetails.patientCare')} percent={doctor.skills.patientCare} />
                  <ProgressBar label={t('doctorDetails.medicalExpertise')} percent={doctor.skills.medicalExpertise} />
                  <ProgressBar label={t('doctorDetails.communication')} percent={doctor.skills.communication} />
                  <ProgressBar label={t('doctorDetails.problemSolving')} percent={doctor.skills.problemSolving} />
                </div>
              </div>
              <div className="col-lg-6 col-md-6 col-sm-12 info-column">
                <div className="inner-box">
                  <h2>{t('doctorDetails.additionalInfo')}</h2>
                  <ul className="info-list clearfix">
                    <li>
                      <strong>{t('doctorDetails.languages')}: </strong>
                      {doctor.languages.join(', ')}
                    </li>
                    <li>
                      <strong>{t('doctorDetails.consultationFee')}: </strong>
                      {t('doctorDetails.contactForPricing')}
                    </li>
                    <li>
                      <strong>{t('doctorDetails.availability')}: </strong>
                      {t('doctorDetails.byAppointment')}
                    </li>
                  </ul>

                  <h4 className="mt_30">{t('doctorDetails.workingHours')}</h4>
                  <ul className="working-hours-list">
                    {Object.entries(doctor.workingHours).map(([day, hours]) => (
                      <li key={day}>
                        <strong>{t(`doctorDetails.days.${day}`)}: </strong>
                        {hours === 'Closed' ? t('doctorDetails.closed') : hours}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="cta-section centred">
            <div className="inner-container">
              <h3>{t('doctorDetails.readyToBook')}</h3>
              <p>{t('doctorDetails.bookingDescription')}</p>
              <div className="btn-box">
                <Link href="/#appointment" className="theme-btn btn-one mr_20">
                  <span>{t('buttons.bookAppointment')}</span>
                </Link>
                <Link href="/#contact" className="theme-btn btn-three">
                  <span>{t('doctorDetails.contactUs')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Custom Styles */}
      <style jsx>{`
        .working-hours-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .working-hours-list li {
          padding: 8px 0;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
          font-size: 16px;
          line-height: 24px;
        }

        .working-hours-list li:last-child {
          border-bottom: none;
        }

        .cta-section {
          background: #f4f3f8;
          padding: 60px 30px;
          border-radius: 10px;
          margin-top: 50px;
        }

        .cta-section h3 {
          font-size: 32px;
          margin-bottom: 15px;
          color: #0e1136;
        }

        .cta-section p {
          font-size: 18px;
          margin-bottom: 30px;
          color: #676767;
        }

        .mr_20 {
          margin-right: 20px;
        }

        .mt_30 {
          margin-top: 30px;
        }

        .info-list li {
          margin-bottom: 15px;
          font-size: 16px;
          line-height: 26px;
        }

        .info-list strong {
          color: #0e1136;
        }

        .rtl .mr_20 {
          margin-right: 0;
          margin-left: 20px;
        }
      `}</style>
    </Layout>
  );
}
