/* Css For Medimart */

/************ TABLE OF CONTENTS ***************
1. Fonts
2. Reset
3. Global
4. Main Header/style-one/style-two
5. Main Slider/style-one/style-two
6. Intro Section
7. Welcome Section
9. Cta Section
8. Research Fields
10. Testimonial Section
11. Researches Section
12. Team Section
14. Video
15. Fact Counter
16. News Section
19. Clients Section
20. Main Footer
21. Footer Bottom
22. Research Style Two
23. Innovative Solution
24. Discover Section
25. Testimonial Section
26. Chooseus Section
27. News Style Two
28. Page Title
29. Research Page Section
30. Research Details
31. Professor <PERSON> Section
32. Professor <PERSON>
33. About Section
34. Error Section
35. Blog Page Section
36. Blog Details
37. Blog Sidebar
38. Contact Section
39. Google Map


**********************************************/
@import url(.//animate.css);
@import url(.//bootstrap.css);
@import url(.//color.css);
@import url(.//elpath.css);
@import url(.//flaticon.css);
@import url(.//font-awesome-all.css);
@import url(.//jquery-ui.css);
@import url(.//jquery.fancybox.min.css);
@import url(.//nice-select.css);
@import url(.//owl.css);
@import url(.//responsive.css);
@import url(.//rtl.css);
@import url(.//switcher-style.css);
@import url(.//timePicker.css);
@import url(.//module-css/about.css);
@import url(.//module-css/appointments.css);
@import url(.//module-css/banner.css);
@import url(.//module-css/blog-details.css);
@import url(.//module-css/chooseus.css);
@import url(.//module-css/contact.css);
@import url(.//module-css/cta.css);
@import url(.//module-css/error.css);
@import url(.//module-css/faq.css);
@import url(.//module-css/feature.css);
@import url(.//module-css/funfact.css);
@import url(.//module-css/gallery.css);
@import url(.//module-css/news.css);
@import url(.//module-css/page-title.css);
@import url(.//module-css/pricing.css);
@import url(.//module-css/process.css);
@import url(.//module-css/service-details.css);
@import url(.//module-css/service.css);
@import url(.//module-css/sidebar.css);
@import url(.//module-css/solutions.css);
@import url(.//module-css/subscribe.css);
@import url(.//module-css/team-details.css);
@import url(.//module-css/team.css);
@import url(.//module-css/testimonial.css);
@import url(.//module-css/video.css);
@import url(.//color/crimson.css);
@import url(.//color/orange.css);
@import url(.//color/pink.css);
@import url(.//color/theme-color.css);
@import url(.//color/violet.css);

/***

====================================================================
  Reset
====================================================================

 ***/
* {
  margin: 0px;
  padding: 0px;
  border: none;
  outline: none;
}

/***

====================================================================
  Global Settings
====================================================================

 ***/

:root {
  --theme-color: #fff;
  --secondary-color: #0C72B1;
  --pink-color: #ffc0cb;
  --violet-color: #fff;
  --crimson-color: #dc143c;
  --orange-color: #ffa500;
  --text-color: #676767;
  --title-color: #0e1136;
}

body {
  font-size: 16px;
  color: var(--text-color);
  line-height: 29px;
  font-weight: 400;
  background: #ffffff;
  font-family: var(--poppins);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center top;
  -webkit-font-smoothing: antialiased;
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
    padding: 0px 15px;
  }
}

.large-container {
  max-width: 1550px;
  padding: 0px 15px;
  margin: 0 auto;
}

.container-fluid {
  padding: 0px;
}

.auto-container {
  position: static;
  max-width: 1320px;
  padding: 0px 15px;
  margin: 0 auto;
}

.small-container {
  max-width: 680px;
  margin: 0 auto;
}

.boxed_wrapper {
  position: relative;
  margin: 0 auto;
  overflow: hidden !important;
  width: 100%;
  min-width: 300px;
}

a {
  text-decoration: none;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

a:hover {
  text-decoration: none;
  outline: none;
}

input,
button,
select,
textarea {
  font-family: var(--text-font);
  font-weight: 400;
  font-size: 18px;
  background: transparent;
}

::-webkit-input-placeholder {
  color: inherit;
}

::-moz-input-placeholder {
  color: inherit;
}

::-ms-input-placeholder {
  color: inherit;
}

ul,
li {
  list-style: none;
  padding: 0px;
  margin: 0px;
}

input {
  transition: all 500ms ease;
}

button:focus,
input:focus,
textarea:focus {
  outline: none;
  box-shadow: none;
  transition: all 500ms ease;
}

p {
  position: relative;
  font-family: var(--text-font);
  color: var(--text-color);
  font-weight: 400;
  margin: 0px;
  transition: all 500ms ease;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  position: relative;
  font-family: var(--poppins);
  color: #101a30;
  margin: 0px;
  transition: all 500ms ease;
}

/* Preloader */

.handle-preloader {
  align-items: center;
  -webkit-align-items: center;
  display: flex;
  display: -ms-flexbox;
  height: 100%;
  justify-content: center;
  -webkit-justify-content: center;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 9999999;
  background: #0e1136;
}

.preloader-close {
  position: fixed;
  z-index: 99999999;
  font-size: 14px;
  background: #fff;
  color: red;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  cursor: pointer;
  right: 30px;
  top: 30px;
  font-weight: 400;
}

.handle-preloader .animation-preloader {
  position: absolute;
  z-index: 100;
}

.handle-preloader .animation-preloader .spinner {
  animation: spinner 1s infinite linear;
  border-radius: 50%;
  height: 150px;
  margin: 0 auto 45px auto;
  width: 150px;
}

.handle-preloader .animation-preloader .txt-loading {
  text-align: center;
  user-select: none;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:before {
  animation: letters-loading 4s infinite;
  content: attr(data-text-preloader);
  left: 0;
  opacity: 0;
  top: 0;
  position: absolute;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading {
  font-family: var(--poppins);
  font-weight: 500;
  letter-spacing: 15px;
  display: inline-block;
  position: relative;
  font-size: 70px;
  line-height: 70px;
  text-transform: uppercase;
  color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: rgba(255, 255, 255, 0.3);
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:nth-child(2):before {
  animation-delay: 0.2s;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:nth-child(3):before {
  animation-delay: 0.4s;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:nth-child(4):before {
  animation-delay: 0.6s;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:nth-child(5):before {
  animation-delay: 0.8s;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:nth-child(6):before {
  animation-delay: 1s;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:nth-child(7):before {
  animation-delay: 1.2s;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:nth-child(8):before {
  animation-delay: 1.4s;
}

.handle-preloader .loader-section {
  background-color: #ffffff;
  height: 100%;
  position: fixed;
  top: 0;
  width: calc(50% + 1px);
}

.preloader .loaded .animation-preloader {
  opacity: 0;
  transition: 0.3s ease-out;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:before {
  color: #ffffff;
}

.handle-preloader .animation-preloader .spinner {
  border: 3px solid #ffffff;
  border-top-color: rgba(255, 255, 255, 0.5);
}

.instagram-posts-grid {

  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* @media screen and (max-width: 600px) {
  .instagram-posts-grid {


    grid-template-columns: repeat(1, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }
} */

/* AnimaciÃ³n del preloader */
@keyframes spinner {
  to {
    transform: rotateZ(360deg);
  }
}

@keyframes letters-loading {

  0%,
  75%,
  100% {
    opacity: 0;
    transform: rotateY(-90deg);
  }

  25%,
  50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
}

@media screen and (max-width: 767px) {
  .handle-preloader .animation-preloader .spinner {
    height: 8em;
    width: 8em;
  }
}

@media screen and (max-width: 500px) {
  .handle-preloader .animation-preloader .spinner {
    height: 7em;
    width: 7em;
  }

  .handle-preloader .animation-preloader .txt-loading .letters-loading {
    font-size: 40px;
    letter-spacing: 10px;
  }
}

.centred {
  text-align: center;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.special_fonts {
  font-family: "Fredoka", sans-serif;
}

figure {
  margin: 0px;
}

img {
  display: inline-block;
  max-width: 100%;
  height: auto;
  transition-delay: 0.1s;
  transition-timing-function: ease-in-out;
  transition-duration: 0.7s;
  transition-property: all;
}

.row {
  --bs-gutter-x: 30px;
}

/** button **/

.theme-btn {
  position: relative;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  font-family: var(--soleil);
  color: #fff !important;
  text-align: center;
  border-radius: 40px;
  z-index: 1;
  box-shadow: 0px 2px 70px rgba(0, 0, 0, 0.1);
  transition: all 500ms ease;
}

.theme-btn.btn-one {
  background: var(--theme-color);
}

.theme-btn.btn-two {
  background: var(--secondary-color);
}

.theme-btn.btn-two:hover,
.theme-btn.btn-one:hover {
  background: #0e1136;
}

.theme-btn:before,
.theme-btn:after {
  content: "";
  position: absolute;
  display: block;
  box-sizing: border-box;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #0e1136;
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
  transform: scale(0) rotate(0);
  z-index: -1;
}

.theme-btn:hover:before {
  border-radius: 0;
  transform: scale(1) rotate(-180deg);
}

.theme-btn:hover:after {
  border-radius: 0;
  transform: scale(1) rotate(180deg);
}

.theme-btn:after {
  background: #0e1d40;
}

.theme-btn span {
  position: relative;
  display: inline-block;
  padding: 24px 48px 22px 48px;
}

.theme-btn span:before {
  position: absolute;
  content: "";
  background: var(--theme-color);
  width: 30px;
  height: 30px;
  left: -5px;
  top: -5px;
  border-radius: 50%;
  transform: scale(0, 0);
  transition: all 500ms ease;
}

.theme-btn:hover span:before {
  transform: scale(1, 1);
}

.theme-btn span:after {
  position: absolute;
  content: "";
  background: var(--theme-color);
  width: 30px;
  height: 30px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  transform: scale(0, 0);
  transition: all 500ms ease;
}

.theme-btn:hover span:after {
  transform: scale(1, 1);
}

.theme-btn.btn-three {
  color: #0e1136 !important;
  border: 1px solid #e5e5e5;
  box-shadow: none;
  background: #fff;
}

.theme-btn.btn-three:before,
.theme-btn.btn-three:after {
  background: var(--theme-color);
}

.theme-btn.btn-three span:before,
.theme-btn.btn-three span:after {
  display: none;
}

.theme-btn.btn-three span {
  padding: 19px 60px 16px 60px;
}

.theme-btn.btn-three:hover {
  color: #fff !important;
  border-color: var(--theme-color);
}

.theme-btn.btn-three i {
  position: relative;
  font-size: 18px;
  margin-left: 10px;
  top: 2px;
}

.parallax-bg {
  position: absolute;
  left: 0px;
  top: -30%;
  width: 100%;
  height: calc(100% + 30%);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

/** styled-pagination */

.pagination {
  position: relative;
  display: block;
}

.pagination li {
  position: relative;
  display: inline-block;
  margin: 0px 4px;
}

.pagination li a {
  position: relative;
  display: inline-block;
  font-size: 22px;
  font-weight: 500;
  height: 40px;
  width: 40px;
  line-height: 40px;
  background: #ffffff;
  border: 1px solid rgba(14, 17, 54, 0.3);
  text-align: center;
  color: var(--title-color);
  border-radius: 5px;
  z-index: 1;
  transition: all 500ms ease;
}

.pagination li a:hover,
.pagination li a.current {
  color: #fff;
  background: var(--theme-color);
  border-color: var(--theme-color);
}

.pagination li a i {
  position: relative;
  font-size: 16px;
}

.sec-pad {
  padding: 114px 0px 90px 0px;
}

.sec-pad-2 {
  padding: 120px 0px;
}

.mr-0 {
  margin: 0px !important;
}

/** scroll-to-top **/

.scroll-to-top {
  position: fixed;
  right: 20px;
  bottom: 60px;
  transform: rotate(0deg);
  z-index: 99;
  width: 50px;
  height: 50px;
  background: var(--theme-color);
  text-align: center;
  color: #fff;
  line-height: 50px;
  border-radius: 50%;
}

.scroll-to-top:hover {
  color: #fff;
  background: #222;
}

.scroll-to-top .visible {
  visibility: visible !important;
  opacity: 1 !important;
}

.scroll-to-top .scroll-top-inner {
  opacity: 1;
  visibility: hidden;
}

.scroll-to-top .scroll-top-inner {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  -o-transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
  transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
}

.scroll-to-top .scroll-bar {
  width: 50px;
  height: 2px;
  margin-right: 10px;
  position: relative;
}

.scroll-to-top .scroll-bar:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #f4244f;
  opacity: 0.3;
}

.scroll-to-top .scroll-bar .bar-inner {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  background-color: currentColor;
}

.scroll-to-top .scroll-bar-text {
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  color: var(--theme-color);
  transition: all 500ms ease;
}

.scroll-to-top .scroll-bar-text:hover {
  transform: scale(1.1);
}

@-webkit-keyframes ripple {
  70% {
    -webkit-box-shadow: 0 0 0 30px rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 30px rgba(255, 255, 255, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

@keyframes ripple {
  70% {
    -webkit-box-shadow: 0 0 0 30px rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 30px rgba(255, 255, 255, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.sec-title {
  position: relative;
  display: block;
}

.sec-title .sub-title {
  position: relative;
  display: inline-block;
  font-size: 24px;
  line-height: 34px;
  font-family: var(--soleil);
  color: var(--theme-color);
  letter-spacing: 0.8px;
  margin-bottom: 15px;
}

.sec-title h2 {
  position: relative;
  display: block;
  font-size: 50px;
  line-height: 65px;
  font-weight: 700;
  margin: 0px;
  text-transform: capitalize;
}

.sec-title.light h2 {
  color: #fff;
}

.sec-title.light .sub-title {
  color: var(--secondary-color) !important;
}

/***

====================================================================
                        Home-Page-One
====================================================================

***/

/** main-header **/

.main-header {
  position: relative;
  left: 0px;
  top: 0px;
  right: 0px;
  z-index: 999;
  width: 100%;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

.sticky-header {
  position: fixed;
  opacity: 0;
  visibility: hidden;
  left: 0px;
  top: 0px;
  width: 100%;
  z-index: 0;
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

.fixed-header .sticky-header {
  z-index: 999;
  opacity: 1;
  visibility: visible;
  -ms-animation-name: fadeInDown;
  -moz-animation-name: fadeInDown;
  -op-animation-name: fadeInDown;
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
  -ms-animation-duration: 500ms;
  -moz-animation-duration: 500ms;
  -op-animation-duration: 500ms;
  -webkit-animation-duration: 500ms;
  animation-duration: 500ms;
  -ms-animation-timing-function: linear;
  -moz-animation-timing-function: linear;
  -op-animation-timing-function: linear;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -ms-animation-iteration-count: 1;
  -moz-animation-iteration-count: 1;
  -op-animation-iteration-count: 1;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}

/** header-top **/

.header-top {
  position: relative;
  width: 100%;
  background: #0e1136;
  padding: 15.5px 0px;
}

.header-top .top-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-top .top-inner .info-list li {
  position: relative;
  display: inline-block;
  color: #fff;
  padding-left: 28px;
  margin-right: 50px;
}

@media only screen and (min-width: 1249px) {
  .info-list {
    width: full;
    display: flex;
  }
}

.location {
  font-size: 8px;
}

.header-top .top-inner .info-list li:last-child {
  margin: 0px !important;
}

.header-top .top-inner .info-list li i {
  position: absolute;
  left: 0px;
  top: 6px;
  font-size: 18px;
  color: #fff;
}

.header-top .top-inner .info-list li a {
  display: inline-block;
  color: #fff;
}

.header-top .top-inner .info-list li a:hover {
  color: var(--secondary-color);
}

.header-top .top-inner .social-links li {
  position: relative;
  display: inline-block;
  float: left;
  margin-right: 30px;
}

.header-top .top-inner .social-links li:last-child {
  margin: 0px !important;
}

.header-top .top-inner .social-links li a {
  display: inline-block;
  font-size: 16px;
  color: #fff;
}

.header-top .top-inner .social-links li a:hover {
  color: var(--secondary-color);
}

/** header-lower **/

.main-header .outer-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.main-header .header-lower {
  position: relative;
  width: 100%;
}

.main-header .header-lower .outer-container {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
}

.main-header .header-lower .outer-box {
  position: relative;
  background: #fff;
}

.main-header .header-lower .outer-box:before {
  position: absolute;
  content: "";
  background: #fff;
  width: 5000px;
  height: 100%;
  left: -1000px;
  top: 0px;
  z-index: -1;
}

.main-header .logo-box {
  position: relative;
  padding: 26px 0px;
}

.main-header .theme-btn span {
  padding: 19px 48px 17px 48px;
}

/*** Search Popup ***/

.search-popup {
  position: fixed;
  left: 0;
  top: 0px;
  width: 100%;
  height: 100%;
  z-index: 99999;
  visibility: hidden;
  opacity: 0;
  overflow: auto;
  background: rgba(0, 0, 0, 0.8);
  -webkit-transform: translateY(101%);
  -ms-transform: translateY(101%);
  transform: translateY(101%);
  transition: all 700ms ease;
  -moz-transition: all 700ms ease;
  -webkit-transition: all 700ms ease;
  -ms-transition: all 700ms ease;
  -o-transition: all 700ms ease;
}

.search-popup.popup-visible {
  -webkit-transform: translateY(0%);
  -ms-transform: translateY(0%);
  transform: translateY(0%);
  visibility: visible;
  opacity: 1;
}

.search-popup .popup-inner {
  width: 100%;
  background: #fff;
  height: 100%;
}

.search-popup .upper-box {
  position: relative;
  padding: 70px 70px;
  z-index: 99;
}

.search-popup .upper-box .logo-box {
  max-width: 182px;
}

.search-popup .overlay-layer {
  position: absolute;
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
  display: block;
}

.search-popup .close-search {
  position: relative;
  font-size: 25px;
  color: #fff;
  cursor: pointer;
  z-index: 5;
  top: 11px;
  transition: all 500ms ease;
}

.search-popup .close-search:hover {
  color: red;
}

.search-popup .search-form {
  position: relative;
  width: 100%;
  padding: 100px 0px 250px 0px;
}

.search-popup .search-form .form-group {
  position: relative;
  margin: 0px;
}

.search-popup .search-form fieldset input[type="search"] {
  position: relative;
  height: 90px;
  padding: 20px 0px;
  background: #ffffff;
  line-height: 30px;
  font-size: 20px;
  color: #808080;
  font-family: "Poppins", sans-serif;
  border: none;
  font-weight: 400;
  border-radius: 0px;
  padding-right: 50px;
  border-bottom: 1px solid #e5e5e5;
}

.search-popup .search-form fieldset button[type="submit"] {
  position: absolute;
  top: 30px;
  right: 0px;
  font-size: 25px;
  color: #fff;
  cursor: pointer;
  transition: all 500ms ease;
}

.search-popup .search-form fieldset input[type="search"]:focus {
  border-color: #fff;
}

.search-popup .form-control:focus {
  box-shadow: none !important;
}

/** main-menu **/

.main-menu {
  float: left;
}

.main-menu .navbar-collapse {
  padding: 0px;
  display: block !important;
}

.main-menu .navigation {
  margin: 0px;
}

.main-menu .navigation>li {
  position: inherit;
  float: left;
  z-index: 2;
  margin: 0px 20px;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}

.main-menu .navigation>li:last-child {
  margin-right: 0px !important;
}

.main-menu .navigation>li:first-child {
  margin-left: 0px !important;
}

.main-menu .navigation>li>a {
  position: relative;
  display: block;
  text-align: center;
  font-size: 20px;
  line-height: 26px;
  padding: 39px 0px 35px 0px;
  font-family: var(--soleil);
  letter-spacing: 0.8px;
  opacity: 1;
  color: #0e1136;
  z-index: 1;
  -webkit-transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  transition: all 500ms ease;
}

.main-menu .navigation>li.current>a,
.main-menu .navigation>li:hover>a {
  color: var(--secondary-color);
}

.main-menu .navigation>li>a:before {
  position: absolute;
  content: "";
  background: rgba(249, 49, 59, 0.1);
  width: 39px;
  height: 39px;
  border-radius: 50%;
  left: -12px;
  top: 32px;
  transform: scale(0, 0);
  transition: all 500ms ease;
}

.main-menu .navigation>li.current>a:before,
.main-menu .navigation>li:hover>a:before {
  transform: scale(1, 1);
}

.main-menu .navigation>li>ul,
.main-menu .navigation>li>.megamenu {
  position: absolute;
  left: inherit;
  top: 100%;
  width: 230px;
  margin-top: 15px;
  z-index: 100;
  display: none;
  background: #0e1136;
  opacity: 0;
  visibility: hidden;
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
  border-radius: 0px;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

.main-menu .navigation>li>ul.from-right {
  left: auto;
  right: 0px;
}

.main-menu .navigation>li>ul>li {
  position: relative;
  width: 100%;
}

.main-menu .navigation>li>ul>li>a,
.main-menu .navigation>li>.megamenu li>a {
  position: relative;
  display: block;
  padding: 10px 25px;
  line-height: 24px;
  font-size: 18px;
  text-transform: capitalize;
  font-family: var(--soleil);
  color: #fff;
  text-align: left;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

.main-menu .navigation>li>ul>li>a {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.main-menu .navigation>li>.megamenu li>a {
  padding-left: 0px;
}

.main-menu .navigation>li>.megamenu h4 {
  display: block;
  font-size: 20px;
  line-height: 30px;
  color: #ffffff;
}

.main-menu .navigation>li>ul>li>a:hover,
.main-menu .navigation>li>.megamenu li>a:hover {
  padding-left: 35px;
  color: var(--secondary-color);
}

.main-menu .navigation>li>ul>li:last-child>a,
.main-menu .navigation>li>.megamenu li:last-child>a {
  border-bottom: none;
}

.main-menu .navigation>li>ul>li.dropdown>a:after {
  font-family: "Font Awesome 5 Pro";
  content: "\f105";
  position: absolute;
  right: 20px;
  top: 10px;
  display: block;
  line-height: 24px;
  font-size: 16px;
  font-weight: 800;
  text-align: center;
  z-index: 5;
}

.main-menu .navigation>li>ul>li>ul {
  position: absolute;
  left: 100%;
  top: 0%;
  margin-top: 15px;
  background: #0e1136;
  width: 230px;
  z-index: 100;
  display: none;
  border-radius: 0px;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

.main-menu .navigation>li>ul>li>ul.from-right {
  left: auto;
  right: 0px;
}

.main-menu .navigation>li>ul>li>ul>li {
  position: relative;
  width: 100%;
}

.main-menu .navigation>li>ul>li>ul>li:last-child {
  border-bottom: none;
}

.main-menu .navigation>li>ul>li>ul>li>a {
  position: relative;
  display: block;
  padding: 10px 25px;
  line-height: 24px;
  font-size: 18px;
  text-transform: capitalize;
  font-family: var(--soleil);
  color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: left;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

.main-menu .navigation>li>ul>li>ul>li:last-child>a {
  border-bottom: none;
}

.main-menu .navigation>li>ul>li>ul>li>a:hover {
  padding-left: 35px;
  color: var(--secondary-color);
}

.main-menu .navigation>li>ul>li>ul>li.dropdown>a:after {
  font-family: "Font Awesome 5 Pro";
  content: "\f105";
  position: absolute;
  right: 20px;
  top: 12px;
  display: block;
  line-height: 24px;
  font-size: 16px;
  font-weight: 900;
  z-index: 5;
}

.main-menu .navigation>li.dropdown:hover>ul,
.main-menu .navigation>li.dropdown:hover>.megamenu {
  visibility: visible;
  opacity: 1;
  margin-top: 0px;
  top: 100%;
}

.main-menu .navigation li>ul>li.dropdown:hover>ul {
  visibility: visible;
  opacity: 1;
  top: 0%;
  margin-top: 0px;
}

.main-menu .navigation li.dropdown .dropdown-btn {
  position: absolute;
  right: -32px;
  top: 66px;
  width: 34px;
  height: 30px;
  text-align: center;
  font-size: 18px;
  line-height: 26px;
  color: #3b3b3b;
  cursor: pointer;
  display: none;
  z-index: 5;
  transition: all 500ms ease;
}

.main-menu .navigation li.current.dropdown .dropdown-btn,
.main-menu .navigation li:hover .dropdown-btn {}

.main-menu .navigation li.dropdown ul li.dropdown .dropdown-btn {
  display: none;
}

.menu-area .mobile-nav-toggler {
  position: relative;
  float: right;
  font-size: 40px;
  line-height: 50px;
  cursor: pointer;
  background: var(--secondary-color);
  display: none;
}

.mobile-menu .nav-logo img {
  max-width: 160px;
}

.menu-area .mobile-nav-toggler .icon-bar {
  position: relative;
  height: 2px;
  width: 30px;
  display: block;
  margin-bottom: 5px;
  background-color: #fff;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}

.header-style-three .menu-area .mobile-nav-toggler .icon-bar {
  background: #222;
}

.menu-area .mobile-nav-toggler .icon-bar:last-child {
  margin-bottom: 0px;
}

/** megamenu-style **/

.main-menu .navigation>li.dropdown>.megamenu {
  position: absolute;
  width: 100%;
  padding: 30px 50px;
  left: 0px;
}

.main-menu .navigation li.dropdown .megamenu li h4 {
  margin-bottom: 10px;
}

.sticky-header .main-menu .navigation>li>a {
  padding-top: 27px;
  padding-bottom: 27px;
}

.sticky-header .main-menu .navigation>li>a:before {
  top: 19px;
}

.sticky-header .main-menu:before {
  top: 15px;
}

/** mobile-menu **/

.nav-outer .mobile-nav-toggler {
  position: relative;
  float: right;
  font-size: 40px;
  line-height: 50px;
  cursor: pointer;
  color: #3786ff;
  display: none;
}

.mobile-menu {
  position: fixed;
  right: 0;
  top: 0;
  width: 300px;
  padding-right: 30px;
  max-width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  z-index: 999999;
  transition: all 900ms ease;
}

.mobile-menu .navbar-collapse {
  display: block !important;
}

.mobile-menu .nav-logo {
  position: relative;
  padding-top: 30px;
  text-align: left;
  padding-bottom: 100px;
}

.mobile-menu {
  padding: 50px 25px;
}

.mobile-menu-visible {
  overflow: hidden;
}

.mobile-menu-visible .mobile-menu {
  opacity: 1;
  visibility: visible;
}

.mobile-menu .menu-backdrop {
  position: fixed;
  left: 0%;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  transition: all 900ms ease;
  background-color: #000;
}

.mobile-menu-visible .mobile-menu .menu-backdrop {
  opacity: 0.7;
  visibility: visible;
  right: 100%;
  -webkit-transition: all 0.8s ease-out 0s;
  -o-transition: all 0.8s ease-out 0s;
}

.mobile-menu .menu-box {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  max-height: 100%;
  overflow-y: auto;
  background: #fff;
  padding: 0px 0px;
  z-index: 5;
  opacity: 0;
  visibility: hidden;
  border-radius: 0px;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
  transition: all 900ms ease !important;
}

.mobile-menu-visible .mobile-menu .menu-box {
  opacity: 1;
  visibility: visible;
  -webkit-transition: all 0.7s ease;
  -o-transition: all 0.7s ease;
  transition: all 0.7s ease;
  -webkit-transform: translateX(0%);
  -ms-transform: translateX(0%);
  transform: translateX(0%);
}

.mobile-menu .close-btn {
  position: absolute;
  right: 25px;
  top: 10px;
  line-height: 30px;
  width: 24px;
  text-align: center;
  font-size: 18px;
  color: #ffffff;
  cursor: pointer;
  z-index: 10;
  -webkit-transition: all 0.9s ease;
  -moz-transition: all 0.9s ease;
  -ms-transition: all 0.9s ease;
  -o-transition: all 0.9s ease;
  transition: all 0.9s ease;
}

.mobile-menu-visible .mobile-menu .close-btn {
  -webkit-transform: rotate(360deg);
  -ms-transform: rotate(360deg);
  transform: rotate(360deg);
}

.mobile-menu .close-btn:hover {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.mobile-menu .navigation {
  position: relative;
  display: block;
  width: 100%;
  float: none;
}

.mobile-menu .navigation li {
  position: relative;
  display: block;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu .navigation:last-child {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu .navigation li>ul>li:first-child {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu .navigation li>a {
  position: relative;
  display: block;
  line-height: 24px;
  padding: 10px 25px;
  font-size: 15px;
  font-weight: 500;
  color: #ffffff;
  text-transform: uppercase;
  -webkit-transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  transition: all 500ms ease;
}

.mobile-menu .navigation li ul li>a {
  font-size: 16px;
  margin-left: 20px;
  text-transform: capitalize;
}

.mobile-menu .navigation li>a:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 0;
  border-left: 5px solid #fff;
  -webkit-transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  transition: all 500ms ease;
}

.mobile-menu .navigation li.current>a:before {
  height: 100%;
}

.mobile-menu .navigation li.dropdown .dropdown-btn {
  position: absolute;
  right: 6px;
  top: 6px;
  width: 32px;
  height: 32px;
  text-align: center;
  font-size: 16px;
  line-height: 32px;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  border-radius: 2px;
  -webkit-transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  transition: all 500ms ease;
  z-index: 5;
}

.mobile-menu .navigation li.dropdown .dropdown-btn.open {
  color: #ffffff;
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.mobile-menu .navigation li>ul,
.mobile-menu .navigation li>ul>li>ul,
.mobile-menu .navigation>li.dropdown>.megamenu {
  display: none;
}

.mobile-menu .social-links {
  position: relative;
  padding: 0px 25px;
}

.mobile-menu .social-links li {
  position: relative;
  display: inline-block;
  margin: 0px 10px 10px;
}

.mobile-menu .social-links li a {
  position: relative;
  line-height: 32px;
  font-size: 16px;
  color: #ffffff;
  -webkit-transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  transition: all 500ms ease;
}

.mobile-menu .social-links li a:hover {}

div#mCSB_1_container {
  top: 0px !important;
}

.mobile-menu .contact-info {
  position: relative;
  padding: 120px 30px 20px 30px;
}

.mobile-menu .contact-info h4 {
  position: relative;
  font-size: 20px;
  color: #ffffff;
  font-weight: 700;
  margin-bottom: 20px;
}

.mobile-menu .contact-info ul li {
  position: relative;
  display: block;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 3px;
}

.mobile-menu .contact-info ul li a {
  color: rgba(255, 255, 255, 0.8);
}

.mobile-menu .contact-info ul li a:hover {}

.mobile-menu .contact-info ul li:last-child {
  margin-bottom: 0px;
}

.main-header .outer-box {
  position: relative;
}

.owl-dots-none .owl-dots,
.owl-nav-none .owl-nav {
  display: none !important;
}

.owl-nav button {
  background: transparent;
}

.float-bob-y {
  animation-name: float-bob-y;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  -webkit-animation-name: float-bob-y;
  -webkit-animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  -moz-animation-name: float-bob-y;
  -moz-animation-duration: 2s;
  -moz-animation-iteration-count: infinite;
  -moz-animation-timing-function: linear;
  -ms-animation-name: float-bob-y;
  -ms-animation-duration: 2s;
  -ms-animation-iteration-count: infinite;
  -ms-animation-timing-function: linear;
  -o-animation-name: float-bob-y;
  -o-animation-duration: 2s;
  -o-animation-iteration-count: infinite;
  -o-animation-timing-function: linear;
}

.float-bob-x {
  animation-name: float-bob-x;
  animation-duration: 15s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  -webkit-animation-name: float-bob-x;
  -webkit-animation-duration: 15s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  -moz-animation-name: float-bob-x;
  -moz-animation-duration: 15s;
  -moz-animation-iteration-count: infinite;
  -moz-animation-timing-function: linear;
  -ms-animation-name: float-bob-x;
  -ms-animation-duration: 15s;
  -ms-animation-iteration-count: infinite;
  -ms-animation-timing-function: linear;
  -o-animation-name: float-bob-x;
  -o-animation-duration: 15s;
  -o-animation-iteration-count: infinite;
  -o-animation-timing-function: linear;
}

/** rtl-switcher **/

.demo-rtl {
  position: fixed;
  top: 390px;
  left: 10px;
  z-index: 9999;
}

button.rtl {
  background: var(--theme-color);
  display: block;
  text-indent: inherit;
  font-size: 12px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-weight: 700;
  margin: 0px;
  color: #fff !important;
  border-radius: 50%;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  transition: all 500ms ease;
}

.demo-ltr {
  position: fixed;
  top: 390px;
  left: auto;
  right: 10px;
  z-index: 9999;
}

button.ltr {
  background: var(--theme-color);
  display: block;
  text-indent: inherit;
  font-size: 12px;
  font-weight: 700;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  margin: 0px;
  color: #fff !important;
  border-radius: 50%;
  box-shadow: rgba(0, 0, 0, 1);
  transition: all 500ms ease;
}

.boxed_wrapper.ltr .demo-rtl {
  display: block;
}

.boxed_wrapper.ltr .demo-ltr {
  display: none;
}

.boxed_wrapper.rtl .demo-rtl {
  display: none;
}

.boxed_wrapper.rtl .demo-ltr {
  display: block;
}

.bg-color-1 {
  background-color: #f4f3f8;
}

.list-style-one li {
  position: relative;
  display: block;
  font-size: 16px;
  line-height: 26px;
  color: #0e1136;
  font-weight: 500;
  padding-left: 40px;
  margin-bottom: 17px;
}

.list-style-one li:before {
  position: absolute;
  content: "\e907";
  font-family: "icomoon";
  width: 30px;
  height: 30px;
  line-height: 30px;
  background: #fff;
  text-align: center;
  border-radius: 50%;
  font-size: 14px;
  color: var(--theme-color);
  left: 0px;
  top: 0px;
}

.border-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid rgba(255, 255, 255, 0.5);
  -webkit-border-radius: 50%;
  -khtml-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  animation: squares 2.9s linear 0s infinite;
  -webkit-animation: squares 2.9s linear 0s infinite;
  -ms-animation: squares 2.9s linear 0s infinite;
  -o-animation: squares 2.9s linear 0s infinite;
  -webkit-animation-play-state: running;
  -moz-animation-play-state: running;
  -o-animation-play-state: running;
  animation-play-state: running;
  opacity: 0;
}

.border-animation.border-2 {
  -webkit-animation-delay: 1s;
  -moz-animation-delay: 1s;
  -o-animation-delay: 1s;
  animation-delay: 1s;
}

.border-animation.border-3 {
  -webkit-animation-delay: 2s;
  -moz-animation-delay: 2s;
  -o-animation-delay: 2s;
  animation-delay: 2s;
}

.check-box input {
  display: none;
}

.check-box label {
  position: relative;
  padding-left: 24px;
  display: inline-block;
  cursor: pointer;
}

.check-box label a {
  color: #676767;
  text-decoration: underline;
}

.check-box label a:hover {
  color: var(--secondary-color);
}

.check-box label:before {
  position: absolute;
  content: "";
  left: 0px;
  top: 7px;
  width: 15px;
  height: 15px;
  border-radius: 3px;
  border-style: solid;
  border-width: 1px;
  border-color: #676767;
  background: transparent;
}

.check-box label:after {
  position: absolute;
  content: "";
  left: 4px;
  top: 11px;
  width: 7px;
  height: 7px;
  border-radius: 2px;
  background: var(--secondary-color);
  opacity: 0;
  transition: all 500ms ease;
}

.check-box input:checked+label:after {
  opacity: 1;
}

/** main-footer **/

.main-footer {
  position: relative;
  background: #0e1136;
}

.main-footer .widget-section {
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.main-footer p,
.main-footer a {
  color: #fff;
}

.main-footer a:hover {
  color: var(--secondary-color);
}

.main-footer .logo-widget .footer-logo {
  position: relative;
  display: block;
  margin-bottom: 30px;
}

.main-footer .logo-widget p {
  margin-bottom: 30px;
}

.main-footer .logo-widget .social-links li {
  position: relative;
  display: inline-block;
  font-size: 16px;
  margin-right: 20px;
}

.main-footer .logo-widget .social-links li:last-child {
  margin: 0px !important;
}

.main-footer .widget-title {
  position: relative;
  display: block;
  margin-bottom: 30px;
}

.main-footer .widget-title h3 {
  font-size: 26px;
  line-height: 36px;
  color: #fff;
  font-weight: 600;
}

.main-footer .links-widget li a {
  font-weight: 500;
  line-height: 40px;
}

.main-footer .contact-widget .info-list li {
  position: relative;
  display: block;
  margin-bottom: 30px;
  color: #fff;
  padding-left: 30px;
}

.main-footer .contact-widget .info-list li:last-child {
  margin-bottom: 0px;
}

.main-footer .contact-widget .info-list li img {
  position: absolute;
  left: 0px;
  top: 6px;
}

.main-footer .contact-widget .info-list li i {
  position: absolute;
  left: 0px;
  top: 6px;
  font-size: 18px;
  color: #fff;
}

.footer-bottom {
  position: relative;
  width: 100%;
  padding: 20px 0px;
}

.footer-bottom .bottom-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-bottom .bottom-inner .footer-nav li {
  position: relative;
  display: inline-block;
  float: left;
  margin-right: 40px;
}

.footer-bottom .bottom-inner .footer-nav li:last-child {
  margin: 0px !important;
}

.main-footer .pattern-layer .pattern-1 {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
}

.main-footer .pattern-layer .pattern-2 {
  position: absolute;
  left: 70px;
  top: 0px;
  width: 145px;
  height: 142px;
  background-repeat: no-repeat;
}

.main-footer .pattern-layer .pattern-3 {
  position: absolute;
  top: 130px;
  right: 90px;
  width: 131px;
  height: 126px;
  background-repeat: no-repeat;
}

.main-footer .pattern-layer .pattern-4 {
  position: absolute;
  left: -300px;
  bottom: -223px;
  width: 544px;
  height: 544px;
  background: #181b3e;
  border-radius: 50%;
  -webkit-animation: zoom-fade 8s infinite linear;
  animation: zoom-fade 8s infinite linear;
}

/** xs-sidebar **/

.xs-sidebar-group .xs-overlay {
  left: 0%;
  top: 0;
  position: fixed;
  height: 100%;
  width: 20%;
  transform: scaleX(0);
  transform-origin: left center;
  cursor: url(../images/icons/cross-out.png), pointer;
  z-index: 9999999;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform 0.65s 0.3s cubic-bezier(0.7, 0, 0.2, 1);
}

.xs-sidebar-group.isActive .xs-overlay {
  transform: scaleX(1);
  transition-delay: 0.03s;
}

.xs-sidebar-group .xs-overlay-2 {
  left: 20%;
  transition-delay: 0.06s;
}

.xs-sidebar-group .xs-overlay-3 {
  left: 40%;
  transition-delay: 0.09s;
}

.xs-sidebar-group .xs-overlay-4 {
  left: 60%;
  transition-delay: 0.12s;
}

.xs-sidebar-group .xs-overlay-5 {
  left: 80%;
  transition-delay: 0.15s;
}

.xs-sidebar-group .widget-heading {
  position: absolute;
  top: 50px;
  right: 35px;
  z-index: 1;
}

.xs-sidebar-widget {
  position: fixed;
  right: -100%;
  top: 0;
  bottom: 0;
  width: 100%;
  max-width: 460px;
  z-index: 999999999;
  height: 100%;
  -webkit-overflow-scrolling: touch;
  background-color: #fff;
  transition: all 900ms ease;
  visibility: hidden;
  opacity: 0;
}

.xs-sidebar-group.isActive .xs-sidebar-widget {
  opacity: 1;
  visibility: visible;
  right: 0;
  -webkit-transition: all 0.7s cubic-bezier(0.9, 0.03, 0, 0.96) 0.6s;
  -o-transition: all 0.7s cubic-bezier(0.9, 0.03, 0, 0.96) 0.6s;
  transition: all 0.7s cubic-bezier(0.9, 0.03, 0, 0.96) 0.6s;
}

.sidebar-textwidget {
  padding: 50px 40px 50px 40px;
}

.xs-sidebar-group .close-side-widget {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 40px;
  cursor: pointer;
  line-height: 40px;
  text-align: center;
  border: 1px solid #0e132d;
  color: #0e132d;
  border-radius: 50%;
  font-weight: 400;
  font-size: 20px;
}

.sidebar-widget-container {
  position: relative;
  opacity: 1;
  visibility: visible;
}

.xs-sidebar-group.isActive .sidebar-widget-container {
  opacity: 1;
  visibility: visible;
}

.xs-bg-black {
  background-color: #101127;
}

.sidebar-info-contents .content-inner {
  position: relative;
}

.sidebar-info-contents .content-inner {
  padding: 0px 0px 40px;
}

.logo {
  height: 30px;
  width: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-img {
  object-fit: cover;
}

.sidebar-info-contents .content-inner .logo img {
  display: inline-block;
  max-width: 100%;
}

.sidebar-info-contents .content-inner .content-box {
  position: relative;
}

.sidebar-info-contents .content-inner .content-box h4 {
  position: relative;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
}

.sidebar-info-contents .content-inner .content-box p {
  position: relative;
  font-size: 15px;
  margin-bottom: 25px;
}

.sidebar-info-contents .content-inner .content-box .theme-btn-two {
  padding: 10px 50px;
}

.sidebar-info-contents .content-inner .contact-info {
  position: relative;
  margin-top: 60px;
}

.sidebar-info-contents .content-inner .contact-info ul li {
  position: relative;
  display: block;
  font-size: 15px;
  color: #676767;
  margin-bottom: 3px;
}

.sidebar-info-contents .content-inner .contact-info ul li a {
  color: #676767;
}

.sidebar-info-contents .content-inner .contact-info ul li a:hover {
  color: var(--secondary-color);
}

.sidebar-info-contents .content-inner .contact-info h4 {
  position: relative;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
}

.sidebar-info-contents .content-inner .social-box {
  position: relative;
  margin-top: 20px;
  margin-bottom: 30px;
}

.sidebar-info-contents .content-inner .social-box li {
  position: relative;
  display: inline-block;
  margin-right: 6px;
  transition: all 900ms ease;
  -moz-transition: all 900ms ease;
  -webkit-transition: all 900ms ease;
  -ms-transition: all 900ms ease;
  -o-transition: all 900ms ease;
}

.sidebar-info-contents .content-inner .social-box li a {
  position: relative;
  width: 36px;
  height: 36px;
  color: #75767b;
  z-index: 1;
  font-size: 13px;
  line-height: 36px;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  background: transparent;
  border: 1px solid #e5e5e5;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}

.sidebar-info-contents .content-inner .social-box li a:hover {
  color: #fff;
  border-color: var(--secondary-color);
  background-color: var(--secondary-color);
}

/** header-style-two **/

.header-style-two .header-top {
  position: relative;
  padding-left: 100px;
  padding-right: 100px;
}

.header-style-two .header-lower .outer-container {
  position: relative;
  padding-left: 100px;
  padding-right: 100px;
  background: #fff;
}

.main-header .menu-right-content {
  position: relative;
  display: flex;
  align-items: center;
}

.main-header .menu-right-content li {
  position: relative;
  display: inline-block;
  font-size: 20px;
  color: #0e1136;
  width: 48px;
  height: 48px;
  line-height: 52px;
  background: #f4f3f8;
  text-align: center;
  border-radius: 50%;
  cursor: pointer;
  margin-right: 40px;
  transition: all 500ms ease;
}

.main-header .menu-right-content li:last-child {
  margin: 0px !important;
}

.main-header .menu-right-content li:hover {
  color: #fff;
  background: var(--secondary-color);
}

.progress-box .bar {
  position: relative;
  width: 100%;
  height: 10px;
  background: rgba(29, 60, 226, 0.1);
  border-radius: 25px;
}

.progress-box .bar-inner {
  position: relative;
  display: block;
  background: var(--theme-color);
  width: 0px;
  height: 10px;
  border-radius: 25px;
  -webkit-transition: all 1500ms ease;
  -ms-transition: all 1500ms ease;
  -o-transition: all 1500ms ease;
  -moz-transition: all 1500ms ease;
  transition: all 1500ms ease;
}

.progress-box {
  position: relative;
  margin-bottom: 30px;
}

.progress-box:last-child {
  margin-bottom: 0px;
}

.progress-box .count-text {
  position: absolute;
  top: -32px;
  right: 0px;
  font-size: 16px;
  line-height: 26px;
  font-weight: 500;
  color: var(--title-color);
}

.progress-box p {
  display: block;
  font-size: 16px;
  line-height: 26px;
  font-weight: 500;
  color: var(--title-color);
  margin-bottom: 10px;
}

.accordion-box .block .acc-content {
  position: relative;
  display: none;
}

.accordion-box .block .acc-content.current {
  display: block;
}

.default-form .form-group {
  position: relative;
  margin-bottom: 20px;
}

.default-form .form-group:last-child {
  margin-bottom: 0px;
}

.default-form .form-group input[type="text"],
.default-form .form-group input[type="email"],
.default-form .form-group textarea {
  position: relative;
  display: block;
  width: 100%;
  height: 70px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 10px 30px;
  font-size: 18px;
  color: #676767;
  transition: all 500ms ease;
}

.default-form .form-group textarea {
  height: 165px;
  resize: none;
}

.default-form .form-group input:focus,
.default-form .form-group textarea:focus {
  border-color: var(--theme-color);
}

.default-form .form-group .theme-btn span {
  padding: 17px 30px 15px 30px;
}

.sidebar-info-contents .content-inner .theme-btn span {
  padding: 17px 40px 15px 40px;
}

/** header-style-three **/

.header-style-three .header-lower .outer-box,
.header-style-three .header-lower .outer-box:before {
  background: transparent;
}

.header-style-three .menu-right-content li {
  background: #f4f3f8;
}

.sticky-header .logo-box {
  padding: 16px 0px;
}

/* Responsive Css */

@media only screen and (max-width: 1749px) {}

@media only screen and (max-width: 1499px) {}

@media only screen and (max-width: 1399px) {}

@media only screen and (max-width: 1299px) {}

@media only screen and (max-width: 1200px) {

  .main-menu,
  .sticky-header,
  .main-header.style-one .outer-container:before {
    display: none !important;
  }

  .menu-area .mobile-nav-toggler {
    display: block;
    padding: 10px;
  }

  .megamenu ul li:first-child {
    display: none;
  }
}

@media only screen and (min-width: 768px) {

  .main-menu .navigation>li>ul,
  .main-menu .navigation>li>ul>li>ul,
  .main-menu .navigation>li>.megamenu {
    display: block !important;
    visibility: hidden;
    opacity: 0;
  }
}

@media only screen and (max-width: 991px) {
  .sec-title h2 br {
    display: none;
  }

  .main-footer .footer-widget {
    margin: 0px 0px 30px 0px !important;
  }

  .main-footer .widget-section {
    padding-bottom: 70px;
  }

  .footer-bottom .bottom-inner {
    display: block;
    text-align: center;
  }

  .footer-bottom .bottom-inner .footer-nav li {
    float: none;
  }
}

@media only screen and (max-width: 767px) {
  .sec-title h2 {
    font-size: 36px;
    line-height: 46px;
  }

  .sec-pad {
    padding: 64px 0px 40px 0px;
  }

  .sec-pad-2 {
    padding: 70px 0px;
  }
}

@media only screen and (max-width: 599px) {
  .header-top {
    display: none;
  }
}

@media only screen and (max-width: 499px) {
  .mobile-menu {
    width: 100%;
  }
}

.btn-container {
  display: flex;
  gap: 10px;
}

/* Default LTR alignment for video section */
.video-section .inner-box {
  text-align: left;
}

.video-section .inner-box h2 {
  text-align: left;
}

@media only screen and (max-width: 599px) {
  .btn-container {
    flex-direction: column;
    gap: 10px;
  }

}