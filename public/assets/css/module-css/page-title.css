
/** page-title **/

.page-title{
  padding: 190px 0px;
}

.page-title .bg-layer{
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.page-title .bg-layer:before{
  position: absolute;
  content: '';
  background: #0E1136;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  opacity: 0.6;
}

.page-title h1{
  display: block;
  font-size: 50px;
  line-height: 60px;
  color: #fff;
  font-weight: 700;
  margin-bottom: 10px;
}

.page-title .bread-crumb li{
  position: relative;
  display: inline-block;
  font-size: 22px;
  line-height: 32px;
  color: #fff;
  font-weight: 500;
  padding-right: 21px;
  margin-right: 3px;
}

.page-title .bread-crumb li:last-child{
  padding: 0px !important;
  margin: 0px !important;
}

.page-title .bread-crumb li a{
  color: var(--secondary-color);
}

.page-title .bread-crumb li a:hover{
  text-decoration: underline;
}

.page-title .bread-crumb li:before{
  position: absolute;
  content: '\e924';
  font-family: 'icomoon';
  top: 0px;
  right: 0px;
  font-weight: 400;
  font-size: 14px;
}

.page-title .bread-crumb li:last-child:before{
  display: none;
}





/** RTL-CSS **/

.rtl .page-title .bread-crumb li{
  padding-right: 0px;
  padding-left: 21px;
  margin-right: 0px;
  margin-left: 3px;
}

.rtl .page-title .bread-crumb li:before{
  right: inherit;
  left: 0px;
}


/** RESPONSIVE-CSS **/

@media only screen and (max-width: 1599px){

}


@media only screen and (max-width: 1200px){


}

@media only screen and (max-width: 991px){



}

@media only screen and (max-width: 767px){

  .page-title{
    padding: 80px 0px;
  }

  .page-title h1{
    font-size: 40px;
    line-height: 50px;
  }

}

@media only screen and (max-width: 599px){


}

@media only screen and (max-width: 499px){



}












































