import "@/node_modules/react-modal-video/css/modal-video.css"
import "../public/assets/css/bootstrap.css"
import "../public/assets/css/color.css"
import "../public/assets/css/style.css"
import "../public/assets/css/rtl.css"
import 'swiper/css'
// import "swiper/css/navigation"
import "swiper/css/pagination"
import 'swiper/css/free-mode';
import { poppins } from '@/lib/font'
import I18nProvider from '@/components/providers/I18nProvider'
export const metadata = {
    title: 'Al-Zain Medical Center',
    description: 'Al-Zain Medical Center in Ras Al Khaimah, UAE, offers expert healthcare services with a commitment to quality, compassion, and patient-centered care. Book appointments online and explore our specialties today.',
}

export default function RootLayout({ children }) {
    return (
        <html lang="en" className={`${poppins.variable}`}>
            <body>
                <I18nProvider>
                    {children}
                </I18nProvider>
            </body>
        </html>
    )
}
