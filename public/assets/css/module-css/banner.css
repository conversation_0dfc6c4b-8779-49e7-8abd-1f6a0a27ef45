
/** banner-section **/

.banner-section{
  position: relative;
  overflow: hidden;
  background-color: #fff;
  padding: 203px 0px 115px 0px;
}

.banner-section .content-box{
  position: relative;
  display: block;
}

.banner-section .content-box .upper-text{
  position: relative;
  display: block;
  font-size: 24px;
  line-height: 34px;
  font-family: var(--secondary-font);
  color: #fff;
  letter-spacing: 0.8px;
  margin-bottom: 15px;
}

.banner-section .content-box h2{
  display: block;
  font-size: 64px;
  line-height: 75px;
  color: #fff;
  font-weight: 700;
  margin-bottom: 30px;
}

.banner-section .content-box h2 span{
  position: relative;
  display: inline-block;
  font-weight: 400;
  color: var(--secondary-color);
}

.banner-section .content-box h2 span:before{
  position: absolute;
  content: '';
  background: var(--secondary-color);
  width: 100%;
  height: 4px;
  left: 0px;
  bottom: 0px;
  border-radius: 10px;
}

.banner-section .content-box p{
  display: block;
  color: #fff;
  margin-bottom: 45px;
}

.banner-section .image-box{
  position: relative;
  display: block;
  margin-left: -25px;
}

.banner-section .image-box img{
  width: 100%;
}
.image-box{
  z-index: 5;
}

.banner-section .pattern-layer{
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
}

.banner-section .shape .shape-1{
  position: absolute;
  left: 40px;
  top: -80px;
  width: 69px;
  height: 77px;
  background-repeat: no-repeat;
  -webkit-animation: zoom-fade 4s infinite linear;
  animation: zoom-fade 4s infinite linear;
}

.banner-section .shape .shape-2{
  position: absolute;
  left: 90px;
  bottom: 10px;
  width: 106px;
  height: 106px;
  background-repeat: no-repeat;
  opacity: 0.6;
}

.banner-section .shape .shape-3{
  position: absolute;
  top: 10px;
  right: 20px;
  width: 157px;
  height: 156px;
  background-repeat: no-repeat;
  -webkit-animation: zoom-fade 6s infinite linear;
  animation: zoom-fade 6s infinite linear;
}

.banner-section .shape .shape-4{
  position: absolute;
  right: 200px;
  bottom: 200px;
  width: 63px;
  height: 70px;
  background-repeat: no-repeat;
  -webkit-animation: zoom-fade 4s infinite linear;
  animation: zoom-fade 4s infinite linear;
}


/** banner-style-two **/

.banner-carousel .swiper-slide{
  position: relative;
  padding: 150px 0px 317px 0px;
}

.banner-carousel .swiper-slide:before{
  position: absolute;
  content: '';
  background: #EBF5FF;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  opacity: 0.8;
  z-index: 1;
}
  
.banner-carousel .swiper-slide .bg-layer{
  position: absolute;
  left:0;
  top:0;
  width:100%;
  height:100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  -webkit-transform:scale(1);
  -ms-transform:scale(1);
  transform:scale(1);
  -webkit-transition: all 8000ms linear;
  -moz-transition: all 8000ms linear;
  -ms-transition: all 8000ms linear;
  -o-transition: all 8000ms linear;
  transition: all 8000ms linear;
}
  
.banner-carousel .swiper-slide-active .bg-layer{
  -webkit-transform:scale(1.25);
  -ms-transform:scale(1.25);
  transform:scale(1.25);
}
  
.banner-carousel .content-box{
  position: relative;
  max-width: 770px;
  width: 100%;
  z-index: 5;
}

.banner-carousel .content-box .upper-text{
  position: relative;
  display: block;
  font-size: 24px;
  line-height: 34px;
  font-family: var(--secondary-font);
  color: var(--theme-color);
  margin-bottom: 15px;
  opacity: 0;
  -webkit-transform: translateY(-10px);
  -moz-transform: translateY(-10px);
  -ms-transform: translateY(-10px);
  -o-transform: translateY(-10px);
  transform: translateY(-10px);
  -webkit-transition: all 1000ms ease;
  -moz-transition: all 1000ms ease;
  -ms-transition: all 1000ms ease;
  -o-transition: all 1000ms ease;
  transition: all 1000ms ease;
}

.banner-carousel .swiper-slide-active .content-box .upper-text{
  opacity: 1;
  -webkit-transition-delay: 700ms;
  -moz-transition-delay: 700ms;
  -ms-transition-delay: 700ms;
  -o-transition-delay: 700ms;
  transition-delay: 700ms;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.banner-carousel .content-box h2{
  position: relative;
  display: block;
  font-size: 64px;
  line-height: 90px;
  font-weight: 700;
  margin-bottom: 25px;
  opacity: 1;
  -webkit-transform: translateY(10px);
  -moz-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
  -webkit-transition: all 1000ms ease;
  -moz-transition: all 1000ms ease;
  -ms-transition: all 1000ms ease;
  -o-transition: all 1000ms ease;
  transition: all 1000ms ease;
}

.banner-carousel .swiper-slide-active .content-box h2{
  opacity: 1;
  -webkit-transition-delay: 700ms;
  -moz-transition-delay: 700ms;
  -ms-transition-delay: 700ms;
  -o-transition-delay: 700ms;
  transition-delay: 700ms;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.banner-carousel .content-box h2 span{
  position: relative;
  display: inline-block;
  font-weight: 400;
  color: var(--secondary-color);
}

.banner-carousel .content-box h2 span:before{
  position: absolute;
  content: '';
  background: var(--secondary-color);
  width: 100%;
  height: 4px;
  left: 0px;
  bottom: 0px;
  border-radius: 5px;
}

.banner-carousel .content-box p{
  position: relative;
  display: block;
  margin-bottom: 50px;
  opacity: 1;
  -webkit-transform: translateY(10px);
  -moz-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
  -webkit-transition: all 1000ms ease;
  -moz-transition: all 1000ms ease;
  -ms-transition: all 1000ms ease;
  -o-transition: all 1000ms ease;
  transition: all 1000ms ease;
}

.banner-carousel .swiper-slide-active .content-box p{
  opacity: 1;
  -webkit-transition-delay: 900ms;
  -moz-transition-delay: 900ms;
  -ms-transition-delay: 900ms;
  -o-transition-delay: 900ms;
  transition-delay: 900ms;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.banner-carousel .content-box .btn-box{
  position: relative;
  display: block;
  opacity: 1;
  -webkit-transform: translateY(10px);
  -moz-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
  -webkit-transition: all 1000ms ease;
  -moz-transition: all 1000ms ease;
  -ms-transition: all 1000ms ease;
  -o-transition: all 1000ms ease;
  transition: all 1000ms ease;
}

.banner-carousel .swiper-slide-active .content-box .btn-box{
  opacity: 1;
  -webkit-transition-delay: 1100ms;
  -moz-transition-delay: 1100ms;
  -ms-transition-delay: 1100ms;
  -o-transition-delay: 1100ms;
  transition-delay: 1100ms;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.banner-carousel .image-layer{
  position: absolute;
  right: 180px;
  bottom: 0px;
  opacity: 0;
  z-index: 1;
  -webkit-transform: translateY(10px);
  -moz-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
  -webkit-transition: all 1000ms ease;
  -moz-transition: all 1000ms ease;
  -ms-transition: all 1000ms ease;
  -o-transition: all 1000ms ease;
  transition: all 1000ms ease;
}

.banner-carousel .swiper-slide-active .image-layer{
  opacity: 1;
  -webkit-transition-delay: 1100ms;
  -moz-transition-delay: 1100ms;
  -ms-transition-delay: 1100ms;
  -o-transition-delay: 1100ms;
  transition-delay: 1100ms;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}


/** banner-style-three **/

.banner-style-three{
  position: relative;
  padding: 118px 0px 91px 0px;
}

.banner-style-three .content-box{
  position: relative;
  display: block;
}

.banner-style-three .content-box .upper-text{
  position: relative;
  display: block;
  font-size: 24px;
  line-height: 24px;
  font-family: var(--secondary-font);
  letter-spacing: 0.8px;
  color: var(--theme-color);
  margin-bottom: 20px;
}

.banner-style-three .content-box h2{
  display: block;
  font-size: 64px;
  line-height: 75px;
  font-weight: 700;
  margin-bottom: 30px;
}

.banner-style-three .content-box h2 span{
  position: relative;
  display: inline-block;
  font-weight: 400;
  color: var(--secondary-color);
}

.banner-style-three .content-box h2 span:before{
  position: absolute;
  content: '';
  background: var(--secondary-color);
  width: 100%;
  height: 4px;
  left: 0px;
  bottom: 0px;
  border-radius: 10px;
}

.banner-style-three .content-box p{
  position: relative;
  display: block;
  margin-bottom: 50px;
}

.banner-style-three .content-box .btn-box{
  position: relative;
  display: flex;
  align-items: center;
}

.banner-style-three .content-box .btn-box .video-btn{
  position: relative;
  display: inline-block;
  width: 70px;
  height: 70px;
  line-height: 70px;
  font-size: 30px;
  text-align: center;
  border-radius: 50%;
  color: var(--theme-color);
  border: 2px solid var(--theme-color);
}

.banner-style-three .content-box .shape{
  position: absolute;
  right: 0px;
  bottom: 0px;
  width: 140px;
  height: 75px;
  background-repeat: no-repeat;
}

.banner-style-three .image-box{
  position: relative;
  padding: 0px 60px 0px 73px;
}

.banner-style-three .image-box .image{
  position: relative;
  display: block;
}

.banner-style-three .image-box .image img{
  width: 100%;
}

.banner-style-three .image-box .image-shape .shape-1{
  position: absolute;
  left: 20px;
  bottom: -70px;
  width: 664px;
  height: 725px;
  background-repeat: no-repeat;
}

.banner-style-three .image-box .image-shape .shape-2{
  position: absolute;
  left: 0px;
  top: 70px;
  width: 29px;
  height: 33px;
  background-repeat: no-repeat;
}

.banner-style-three .image-box .image-shape .shape-3{
  position: absolute;
  right: 0px;
  bottom: 0px;
  width: 29px;
  height: 33px;
  background-repeat: no-repeat;
}

.banner-style-three .image-box .image-shape .shape-4{
  position: absolute;
  top: 50px;
  right: 0px;
  width: 89px;
  height: 88px;
  background-repeat: no-repeat;
}

.banner-style-three .pattern-layer{
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 1344px;
  background-size: cover;
  background-repeat: no-repeat;
}

.banner-style-three .image-box .text-box{
  position: absolute;
  right: -160px;
  bottom: 130px;
  width: 350px;
  background: #fff;
  box-shadow: 0px 2px 70px rgba(0, 0, 0, 0.1);
  border-radius: 0px 60px;
  text-align: center;
  padding: 31px 30px;
}

.banner-style-three .image-box .text-box h3{
  display: block;
  font-size: 22px;
  line-height: 31px;
  font-weight: 500;
}

.banner-style-three .image-box .text-box .designation{
  position: relative;
  display: block;
  font-size: 16px;
  line-height: 26px;
  font-weight: 500;
  color: var(--secondary-font);
}





/** RTL-CSS **/

.rtl .banner-carousel .content-box{
  text-align: right;
}

.rtl .banner-style-three .content-box .btn-box .theme-btn{
  margin-right: 0px;
  margin-left: 60px;
}

.rtl .banner-style-three .content-box .shape{
  right: inherit;
  left: 0px;
}

.rtl .banner-style-three .image-box .text-box{
  right: inherit;
  left: -160px;
}



/** RESPONSIVE-CSS **/
@media screen and (max-width: 800px) {
  .banner-section .content-box h2 {
    font-size: 34px;
  }
  .banner-section .content-box p{
    font-size: 16px;
  }
  .banner-section .content-box .upper-text span {
    font-size: 10px;
  }
  
}
@media screen and (min-width: 810px) and (max-width: 1910px) {
  .banner-section .content-box h2 {
    font-size: 44px;
  }
  .banner-section .content-box p{
    font-size: 20px;
  }
}
@media screen and (min-width: 1990px) {
  .banner-section .content-box h2 {
    font-size: 64px;
  }
  .banner-section .content-box p{
    font-size: 28px;
  }
}

@media screen and (min-width: 3839px) {
  html {
    font-size: 35px;
  }
}
@media screen and (min-width: 7670px) {
  html {
    font-size: 75px;
  }
}
/* ssssssssss */
@media only screen and (max-width: 1599px){
  .banner-style-three .image-box .text-box{
    right: 0px;
  }

  .rtl .banner-style-three .image-box .text-box{
    left: 0px;
  }
}


@media only screen and (max-width: 1200px){

  .banner-carousel .image-layer{
    right: 0px;
  }

}

@media only screen and (max-width: 991px){

  .banner-section .shape{
    display: none;
  }

  .banner-section .content-box{
    margin-bottom: 40px;
  }

  .banner-section .image-box{
    margin: 0px;
  }

  .banner-carousel .image-layer{
    display: none;
  }

  .banner-style-three{
    padding-top: 180px;
  }

  .banner-style-three .content-box{
    margin-bottom: 30px;
  }

}

@media only screen and (max-width: 767px){

  .banner-section .content-box h2,
  .banner-style-three .content-box h2{
    font-size: 50px;
    line-height: 60px;
  }

  .banner-carousel .content-box h2{
    font-size: 50px;
    line-height: 70px;
  }

  .banner-style-three .content-box .shape{
    display: none;
  }

  .banner-style-three .image-box .image-shape{
    display: none;
  }

  .banner-style-three{
    padding-bottom: 30px;
  }

}

@media only screen and (max-width: 599px){

  .banner-style-three .image-box{
    padding: 0px;
  }

}

@media only screen and (max-width: 499px){

  .banner-style-three .content-box .btn-box{
    display: block;
  }

  .rtl .banner-style-three .content-box .btn-box .theme-btn{
    margin-left: 0px;
  }

  .banner-style-three .content-box .btn-box .theme-btn{
    margin-right: 0px;
    margin-bottom: 20px;
  }

  .banner-style-three .image-box .text-box{
    position: relative;
    width: 100%;
    bottom: 0px;
    margin-top: 30px;
  }

}












































