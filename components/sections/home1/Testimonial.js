'use client';
import TestimonialSlider1 from '@/components/slider/TestimonialSlider1'
import { useTranslation } from 'react-i18next';

export default function Testimonial() {
  const { t } = useTranslation('common');
  return (
    <>

      <section className="testimonial-section sec-pad bg-color-1" style={{background: 'white'}}>
        <div className="auto-container">
          <div className="row clearfix">
            <div className="col-xl-6 col-lg-12 col-md-12 offset-xl-3 content-column">
              <div className="content-box p_relative ml_45">
                <div className="sec-title mb_50">
                  <span className="sub-title">{t('testimonials.subtitle')}</span>
                  <h2>{t('testimonials.title')}</h2>
                </div>
                <div className="content-box">
                  <TestimonialSlider1 />
                </div>
              </div>

            </div>
          </div>
        </div>
      </section>
    </>
  )
}
