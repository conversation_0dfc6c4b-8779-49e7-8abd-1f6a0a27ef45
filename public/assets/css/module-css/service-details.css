
/** service-details **/

.service-details{
  position: relative;
}

.service-details-content{
  position: relative;
}

.service-details-content .image-box{
  position: relative;
  display: block;
  border-radius: 20px;
}

.service-details-content .image-box img{
  width: 100%;
  border-radius: 20px;
}

.service-details-content h2{
  display: block;
  font-size: 36px;
  line-height: 46px;
  font-weight: 600;
  margin-bottom: 30px;
}

.service-details-content .content-two p{
  margin-bottom: 30px;
}

.service-details-content .content-two p:last-child{
  margin-bottom: 0px;
}





/** RTL-CSS **/





/** RESPONSIVE-CSS **/

@media only screen and (max-width: 1599px){

}


@media only screen and (max-width: 1200px){


}

@media only screen and (max-width: 991px){



}

@media only screen and (max-width: 767px){



}

@media only screen and (max-width: 599px){



}

@media only screen and (max-width: 499px){



}












































