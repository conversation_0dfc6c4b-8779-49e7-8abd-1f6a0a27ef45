'use client';

import { useTranslation } from 'react-i18next';
import LanguageSwitcher from '@/components/elements/LanguageSwitcher';

export default function TestI18n() {
  const { t, i18n } = useTranslation('common');

  return (
    <div style={{ padding: '40px', maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ marginBottom: '30px', textAlign: 'center' }}>
        <h1>i18next Localization Test</h1>
        <LanguageSwitcher />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <p><strong>Current Language:</strong> {i18n.language}</p>
        <p><strong>Direction:</strong> {i18n.language === 'ar' ? 'RTL' : 'LTR'}</p>
      </div>

      <div style={{
        border: '1px solid #ddd',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2>Navigation Translations</h2>
        <ul>
          <li><strong>Home:</strong> {t('navigation.home')}</li>
          <li><strong>About Us:</strong> {t('navigation.aboutUs')}</li>
          <li><strong>Our Services:</strong> {t('navigation.ourServices')}</li>
          <li><strong>Our Team:</strong> {t('navigation.ourTeam')}</li>
          <li><strong>Appointment:</strong> {t('navigation.appointment')}</li>
        </ul>
      </div>

      <div style={{
        border: '1px solid #ddd',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2>Header Information</h2>
        <ul>
          <li><strong>Open Hours:</strong> {t('header.openHours')}</li>
          <li><strong>Phone:</strong> {t('header.phone')}</li>
          <li><strong>Location:</strong> {t('header.location')}</li>
        </ul>
      </div>

      <div style={{
        border: '1px solid #ddd',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2>Common Actions</h2>
        <ul>
          <li><strong>Book Appointment:</strong> {t('buttons.bookAppointment')}</li>
          <li><strong>Get Appointment:</strong> {t('buttons.getAppointment')}</li>
          <li><strong>Send Message:</strong> {t('buttons.sendMessage')}</li>
          <li><strong>Read More:</strong> {t('buttons.readMore')}</li>
        </ul>
      </div>

      <div style={{
        border: '1px solid #ddd',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2>About Section</h2>
        <h3>{t('about.title')}</h3>
        <p>{t('about.description')}</p>
      </div>

      <div style={{
        border: '1px solid #ddd',
        padding: '20px',
        borderRadius: '8px'
      }}>
        <h2>Contact Information</h2>
        <ul>
          <li><strong>Contact Us:</strong> {t('contact.contactUs')}</li>
          <li><strong>First Name:</strong> {t('contact.firstName')}</li>
          <li><strong>Last Name:</strong> {t('contact.lastName')}</li>
          <li><strong>Email:</strong> {t('contact.email')}</li>
          <li><strong>Phone Number:</strong> {t('contact.phoneNumber')}</li>
          <li><strong>Message:</strong> {t('contact.message')}</li>
        </ul>
      </div>

      <div style={{ marginTop: '30px', textAlign: 'center' }}>
        <a href="/" style={{
          display: 'inline-block',
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          textDecoration: 'none',
          borderRadius: '5px'
        }}>
          {t('navigation.home')}
        </a>
      </div>
    </div>
  );
}
